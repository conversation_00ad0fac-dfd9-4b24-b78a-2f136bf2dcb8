/**
 * 颜色常量定义 - 静态常量集中管理
 * 🎯 职责：颜色映射、中文名称、编码、优先级等纯常量定义
 * 📦 数据源：原styleStore.ts中的颜色常量
 * ✅ Phase 4.7.1: 职责分离 - 静态常量与状态管理分离
 */

// 基础颜色类型定义
export type ColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';
export type BasicColorType = ColorType | 'black';

// 颜色CSS映射常量
export const COLOR_CSS_MAP: Record<ColorType, Record<string, string>> = {
  red: {
    text: 'text-red-500',
    border: 'border-red-500', 
    bg: 'bg-red-500',
    hoverBg: 'hover:bg-red-100',
    hoverText: 'hover:text-red-800',
    level1: 'bg-red-500',
    level2: 'bg-red-600',
    level3: 'bg-red-400',
    level4: 'bg-red-300',
  },
  cyan: {
    text: 'text-cyan-500',
    border: 'border-cyan-500',
    bg: 'bg-cyan-500', 
    hoverBg: 'hover:bg-cyan-100',
    hoverText: 'hover:text-cyan-800',
    level1: 'bg-cyan-500',
    level2: 'bg-cyan-600',
    level3: 'bg-cyan-400',
    level4: 'bg-cyan-300',
  },
  yellow: {
    text: 'text-yellow-500',
    border: 'border-yellow-500',
    bg: 'bg-yellow-500',
    hoverBg: 'hover:bg-yellow-100', 
    hoverText: 'hover:text-yellow-800',
    level1: 'bg-yellow-500',
    level2: 'bg-yellow-600',
    level3: 'bg-yellow-400',
    level4: 'bg-yellow-300',
  },
  purple: {
    text: 'text-purple-500',
    border: 'border-purple-500',
    bg: 'bg-purple-500',
    hoverBg: 'hover:bg-purple-100',
    hoverText: 'hover:text-purple-800',
    level1: 'bg-purple-500',
    level2: 'bg-purple-600',
    level3: 'bg-purple-400',
    level4: 'bg-purple-300',
  },
  orange: {
    text: 'text-orange-500',
    border: 'border-orange-500',
    bg: 'bg-orange-500',
    hoverBg: 'hover:bg-orange-100',
    hoverText: 'hover:text-orange-800',
    level1: 'bg-orange-500',
    level2: 'bg-orange-600',
    level3: 'bg-orange-400',
    level4: 'bg-orange-300',
  },
  green: {
    text: 'text-green-500',
    border: 'border-green-500',
    bg: 'bg-green-500',
    hoverBg: 'hover:bg-green-100',
    hoverText: 'hover:text-green-800',
    level1: 'bg-green-500',
    level2: 'bg-green-600',
    level3: 'bg-green-400',
    level4: 'bg-green-300',
  },
  blue: {
    text: 'text-blue-500',
    border: 'border-blue-500',
    bg: 'bg-blue-500',
    hoverBg: 'hover:bg-blue-100',
    hoverText: 'hover:text-blue-800',
    level1: 'bg-blue-500',
    level2: 'bg-blue-600',
    level3: 'bg-blue-400',
    level4: 'bg-blue-300',
  },
  pink: {
    text: 'text-pink-500',
    border: 'border-pink-500',
    bg: 'bg-pink-500',
    hoverBg: 'hover:bg-pink-100',
    hoverText: 'hover:text-pink-800',
    level1: 'bg-pink-500',
    level2: 'bg-pink-600',
    level3: 'bg-pink-400',
    level4: 'bg-pink-300',
  },
};

// 黑色特殊处理
export const BLACK_CSS_MAP = {
  text: 'text-black',
  border: 'border-black',
  bg: 'bg-black',
  hoverBg: 'hover:bg-gray-100',
  hoverText: 'hover:text-gray-800',
  level1: 'bg-black',
  level2: 'bg-gray-700',
  level3: 'bg-gray-500',
  level4: 'bg-gray-300',
} as const;

// 颜色中文名称映射
export const COLOR_NAMES: Record<BasicColorType, string> = {
  black: '黑色',
  red: '红色',
  orange: '橙色', 
  cyan: '青色',
  yellow: '黄色',
  purple: '紫色',
  green: '绿色',
  blue: '蓝色',
  pink: '粉色',
} as const;

// 颜色简短名称映射
export const COLOR_SHORT_NAMES: Record<BasicColorType, string> = {
  black: '黑',
  red: '红',
  orange: '橙',
  yellow: '黄', 
  green: '绿',
  cyan: '青',
  blue: '蓝',
  purple: '紫',
  pink: '粉',
} as const;

// 颜色数字编码映射
export const COLOR_NUMBER_MAP: Record<Exclude<BasicColorType, 'black'>, string> = {
  red: '1',
  orange: '2', 
  yellow: '3',
  green: '4',
  cyan: '5',
  blue: '6',
  purple: '7',
  pink: '8',
} as const;

// 颜色优先级排序
export const COLOR_PRIORITY_ORDER: BasicColorType[] = [
  'red', 'cyan', 'yellow', 'purple', 
  'orange', 'green', 'blue', 'pink', 
  'black'
] as const; 