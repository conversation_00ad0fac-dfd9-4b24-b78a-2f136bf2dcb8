/**
 * 样式常量定义 - 静态样式配置集中管理
 * 🎯 职责：按钮样式、UI配置、主题配置等纯常量定义
 * 📦 数据源：原styleStore.ts中的样式常量
 * ✅ Phase 4.7.1: 职责分离 - 静态常量与状态管理分离
 */

// 按钮样式类型定义
export type ButtonVariant = 'primary' | 'secondary' | 'active' | 'inactive' | 'danger' | 'success' | 'neutral';
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg';

// 按钮尺寸样式
export const SIZE_STYLES: Record<ButtonSize, string> = {
  xs: 'px-2 py-1 text-xs',
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-sm',
  lg: 'px-6 py-3 text-base',
};

// 按钮变体样式
export const VARIANT_STYLES: Record<ButtonVariant, string> = {
  primary: 'bg-blue-600 text-white border-blue-500 hover:bg-blue-700',
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200',
  active: 'bg-gray-200 text-gray-800 border-gray-400',
  inactive: 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700',
  danger: 'bg-red-600 text-white border-red-500 hover:bg-red-700',
  success: 'bg-green-600 text-white border-green-500 hover:bg-green-700',
  neutral: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',
};

// 按钮基础样式
export const BASE_BUTTON_STYLES = 'rounded border transition-colors duration-200 flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2';

// 按钮状态样式组合（保持向后兼容）
export const BUTTON_STYLES = {
  active: 'bg-gray-200 text-gray-800 border-gray-400',
  inactive: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',
  primary: 'bg-gray-200 text-gray-800 border-gray-400 hover:bg-gray-300',
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200',
} as const;

// Tab切换样式
export const TAB_STYLES = {
  active: 'bg-white text-gray-800 border-gray-400 shadow-sm',
  inactive: 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200',
} as const;

// 输入框样式
export const INPUT_STYLES = {
  default: 'px-2 py-1 text-xs border border-gray-200 rounded focus:border-gray-400 focus:outline-none',
  center: 'text-center',
} as const;

// 网格布局样式
export const GRID_STYLES = {
  cols2: 'grid-cols-2',
  cols3: 'grid-cols-3', 
  cols4: 'grid-cols-4',
  cols5: 'grid-cols-5',
} as const;

// 默认样式配置
export const DEFAULT_MATRIX_STYLES = {
  container: 'flex-1 relative overflow-hidden bg-gray-100',
  gridLayout: 'grid gap-1 p-4',
  cellBase: 'aspect-square flex items-center justify-center text-xs font-medium cursor-pointer border border-gray-300 transition-all duration-200',
  cellHover: 'hover:scale-105 hover:z-10',
} as const;

export const DEFAULT_CONTROL_PANEL_STYLES = {
  container: 'w-80 h-full bg-white border-l border-gray-200 flex flex-col shadow-lg',
  header: 'p-4 border-b border-gray-200 bg-gray-50',
  content: 'flex-1 p-4 overflow-y-auto',
  footer: 'p-4 border-t border-gray-200 bg-gray-50',
} as const;

export const DEFAULT_BUTTON_STYLES = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
  secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
} as const;

export const DEFAULT_COLOR_SCHEME = {
  background: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    tertiary: '#f1f5f9',
  },
  text: {
    primary: '#0f172a',
    secondary: '#475569',
    accent: '#3b82f6',
  },
  border: {
    primary: '#e2e8f0',
    secondary: '#cbd5e1',
    focus: '#3b82f6',
  },
} as const;

// 样式接口定义
export interface MatrixStyles {
  container: string;
  gridLayout: string;
  cellBase: string;
  cellHover: string;
}

export interface ControlPanelStyles {
  container: string;
  header: string;
  content: string;
  footer: string;
}

export interface ButtonStyles {
  primary: string;
  secondary: string;
  danger: string;
  success: string;
}

export interface ColorScheme {
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  text: {
    primary: string;
    secondary: string;
    accent: string;
  };
  border: {
    primary: string;
    secondary: string;
    focus: string;
  };
} 