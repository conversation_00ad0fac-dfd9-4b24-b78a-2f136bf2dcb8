/**
 * 通用表单处理Hook
 * 🎯 统一管理重复的事件处理模式，减少代码重复
 * 📈 解决onChange、onClick、onSubmit等重复处理逻辑，提升开发效率
 */

import { useCallback, useState, useMemo } from 'react';
import type { CellData } from '../types/grid';
import type { ColorType } from '../stores';

// 通用事件处理器类型
export type EventHandler<T = any> = (value: T) => void;
export type ToggleHandler = () => void;
export type ClickHandler = (event?: React.MouseEvent) => void;
export type ChangeHandler<T = string> = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;

/**
 * 通用状态切换Hook
 * @param initialValue 初始值
 * @returns [当前值, 切换函数, 设置函数]
 */
export const useToggle = (initialValue: boolean = false) => {
  const [value, setValue] = useState(initialValue);
  
  const toggle = useCallback(() => {
    setValue(prev => !prev);
  }, []);
  
  const setTrue = useCallback(() => setValue(true), []);
  const setFalse = useCallback(() => setValue(false), []);
  
  return [value, toggle, setValue, setTrue, setFalse] as const;
};

/**
 * 数值范围输入处理Hook
 * @param initialValue 初始值
 * @param min 最小值
 * @param max 最大值
 * @param step 步长
 * @returns [当前值, 变更处理器, 设置函数]
 */
export const useRangeInput = (
  initialValue: number,
  min: number = 0,
  max: number = 100,
  step: number = 1
) => {
  const [value, setValue] = useState(initialValue);
  
  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(event.target.value);
    if (newValue >= min && newValue <= max) {
      setValue(newValue);
    }
  }, [min, max]);
  
  const handleDirectChange = useCallback((newValue: number) => {
    if (newValue >= min && newValue <= max) {
      setValue(newValue);
    }
  }, [min, max]);
  
  return [value, handleChange, setValue, handleDirectChange] as const;
};

/**
 * 选择器处理Hook（用于单选、多选等场景）
 * @param initialValue 初始选中值
 * @param onSelectionChange 选择变更回调
 * @returns [当前选中值, 切换选择函数, 是否选中检查函数, 设置选择函数]
 */
export const useSelection = <T>(
  initialValue: Set<T> = new Set(),
  onSelectionChange?: (selection: Set<T>) => void
) => {
  const [selection, setSelection] = useState<Set<T>>(initialValue);
  
  const toggleSelection = useCallback((item: T) => {
    const newSelection = new Set(selection);
    if (newSelection.has(item)) {
      newSelection.delete(item);
    } else {
      newSelection.add(item);
    }
    setSelection(newSelection);
    onSelectionChange?.(newSelection);
  }, [selection, onSelectionChange]);
  
  const isSelected = useCallback((item: T) => {
    return selection.has(item);
  }, [selection]);
  
  const setSelectionDirect = useCallback((newSelection: Set<T>) => {
    setSelection(newSelection);
    onSelectionChange?.(newSelection);
  }, [onSelectionChange]);
  
  const clearSelection = useCallback(() => {
    const newSelection = new Set<T>();
    setSelection(newSelection);
    onSelectionChange?.(newSelection);
  }, [onSelectionChange]);
  
  const selectAll = useCallback((items: T[]) => {
    const newSelection = new Set(items);
    setSelection(newSelection);
    onSelectionChange?.(newSelection);
  }, [onSelectionChange]);
  
  return [
    selection,
    toggleSelection,
    isSelected,
    setSelectionDirect,
    clearSelection,
    selectAll
  ] as const;
};

/**
 * 单选处理Hook
 * @param initialValue 初始选中值
 * @param onValueChange 值变更回调
 * @returns [当前值, 变更处理器, 是否选中检查函数]
 */
export const useSingleSelect = <T>(
  initialValue: T | null = null,
  onValueChange?: (value: T | null) => void
) => {
  const [value, setValue] = useState<T | null>(initialValue);
  
  const handleSelect = useCallback((item: T) => {
    setValue(item);
    onValueChange?.(item);
  }, [onValueChange]);
  
  const isSelected = useCallback((item: T) => {
    return value === item;
  }, [value]);
  
  const clear = useCallback(() => {
    setValue(null);
    onValueChange?.(null);
  }, [onValueChange]);
  
  return [value, handleSelect, isSelected, clear] as const;
};

/**
 * 表单输入处理Hook
 * @param initialValues 初始表单值
 * @returns [表单值, 输入处理器, 设置值函数, 重置函数]
 */
export const useFormInput = <T extends Record<string, any>>(
  initialValues: T
) => {
  const [values, setValues] = useState<T>(initialValues);
  
  const handleInputChange = useCallback((
    field: keyof T,
    value: any
  ) => {
    setValues(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);
  
  const handleEventChange = useCallback((field: keyof T) => 
    (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { value, type } = event.target;
      const finalValue = type === 'checkbox' ? (event.target as HTMLInputElement).checked : value;
      handleInputChange(field, finalValue);
    }, [handleInputChange]);
  
  const reset = useCallback(() => {
    setValues(initialValues);
  }, [initialValues]);
  
  const setFieldValue = useCallback((field: keyof T, value: any) => {
    handleInputChange(field, value);
  }, [handleInputChange]);
  
  return [values, handleEventChange, setValues, reset, setFieldValue] as const;
};

/**
 * 按钮状态管理Hook（用于处理加载、禁用等状态）
 * @param initialLoading 初始加载状态
 * @returns [状态对象, 状态控制函数]
 */
export const useButtonState = (initialLoading: boolean = false) => {
  const [loading, setLoading] = useState(initialLoading);
  const [disabled, setDisabled] = useState(false);
  
  const startLoading = useCallback(() => {
    setLoading(true);
    setDisabled(true);
  }, []);
  
  const stopLoading = useCallback(() => {
    setLoading(false);
    setDisabled(false);
  }, []);
  
  const enable = useCallback(() => setDisabled(false), []);
  const disable = useCallback(() => setDisabled(true), []);
  
  return [
    { loading, disabled },
    { startLoading, stopLoading, enable, disable, setLoading, setDisabled }
  ] as const;
};

/**
 * 级别控制Hook（用于颜色级别切换等场景）
 * @param availableLevels 可用级别列表
 * @param initialActivelevels 初始激活级别
 * @param onLevelChange 级别变更回调
 * @returns [激活级别Set, 切换级别函数, 批量设置函数]
 */
export const useLevelControl = (
  availableLevels: (1 | 2 | 3 | 4)[],
  initialActiveLevels: Set<1 | 2 | 3 | 4> = new Set(),
  onLevelChange?: (levels: Set<1 | 2 | 3 | 4>) => void
) => {
  const [activeLevels, setActiveLevels] = useState<Set<1 | 2 | 3 | 4>>(initialActiveLevels);
  
  const toggleLevel = useCallback((level: 1 | 2 | 3 | 4) => {
    if (!availableLevels.includes(level)) return;
    
    const newLevels = new Set(activeLevels);
    if (newLevels.has(level)) {
      newLevels.delete(level);
    } else {
      newLevels.add(level);
    }
    setActiveLevels(newLevels);
    onLevelChange?.(newLevels);
  }, [activeLevels, availableLevels, onLevelChange]);
  
  const setLevels = useCallback((levels: Set<1 | 2 | 3 | 4>) => {
    setActiveLevels(levels);
    onLevelChange?.(levels);
  }, [onLevelChange]);
  
  const enableAllLevels = useCallback(() => {
    const allLevels = new Set(availableLevels);
    setActiveLevels(allLevels);
    onLevelChange?.(allLevels);
  }, [availableLevels, onLevelChange]);
  
  const disableAllLevels = useCallback(() => {
    const emptyLevels = new Set<1 | 2 | 3 | 4>();
    setActiveLevels(emptyLevels);
    onLevelChange?.(emptyLevels);
  }, [onLevelChange]);
  
  return [activeLevels, toggleLevel, setLevels, enableAllLevels, disableAllLevels] as const;
};

/**
 * 版本管理Hook（用于版本切换、保存、删除等操作）
 * @param versions 版本列表
 * @param currentVersion 当前版本
 * @param onVersionChange 版本变更回调
 * @returns [版本状态, 版本操作函数]
 */
export const useVersionControl = <T = any>(
  versions: Record<string, T>,
  currentVersion: string,
  onVersionChange?: (version: string, data?: T) => void
) => {
  const [newVersionName, setNewVersionName] = useState('');
  const [selectedVersion, setSelectedVersion] = useState(currentVersion);
  
  const switchVersion = useCallback((versionName: string) => {
    if (versions[versionName]) {
      setSelectedVersion(versionName);
      onVersionChange?.(versionName, versions[versionName]);
    }
  }, [versions, onVersionChange]);
  
  const handleVersionNameChange = useCallback((
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setNewVersionName(event.target.value);
  }, []);
  
  const clearVersionName = useCallback(() => {
    setNewVersionName('');
  }, []);
  
  const isVersionExists = useCallback((name: string) => {
    return name in versions;
  }, [versions]);
  
  return [
    {
      newVersionName,
      selectedVersion,
      versionList: Object.keys(versions)
    },
    {
      switchVersion,
      handleVersionNameChange,
      clearVersionName,
      setNewVersionName,
      isVersionExists
    }
  ] as const;
};

/**
 * 组合多个事件处理器
 * @param handlers 处理器数组
 * @returns 组合后的处理器
 */
export const combineHandlers = <T = any>(...handlers: ((value: T) => void)[]) => {
  return useCallback((value: T) => {
    handlers.forEach(handler => handler?.(value));
  }, [handlers]);
};

/**
 * 防抖处理Hook
 * @param callback 回调函数
 * @param delay 延迟时间
 * @returns 防抖后的函数
 */
export const useDebounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T => {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
  
  return useCallback((...args: Parameters<T>) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    
    const timer = setTimeout(() => {
      callback(...args);
    }, delay);
    
    setDebounceTimer(timer);
  }, [callback, delay, debounceTimer]) as T;
};

/**
 * 节流处理Hook
 * @param callback 回调函数
 * @param delay 间隔时间
 * @returns 节流后的函数
 */
export const useThrottle = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T => {
  const [lastCall, setLastCall] = useState(0);
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      setLastCall(now);
      callback(...args);
    }
  }, [callback, delay, lastCall]) as T;
};

/**
 * 性能优化Hook - Phase 5.2 核心性能函数优化
 * 提供记忆化的渲染性能优化功能
 */
export const usePerformanceOptimized = () => {
    // 1. 颜色优先级常量（记忆化）
    const colorPriority = useMemo<ColorType[]>(() => [
        'red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'
    ], []);

    // 2. 颜色映射常量（记忆化）
    const colorMap = useMemo(() => ({
        red: '1', orange: '2', yellow: '3', green: '4', 
        cyan: '5', blue: '6', purple: '7', pink: '8'
    }), []);

    // 3. 样式类常量（记忆化）
    const baseClasses = useMemo(() => [
        'transition-all', 'duration-200', 'relative', 'z-10'
    ], []);

    // 4. 默认样式配置（记忆化）
    const defaultStyles = useMemo(() => ({
        bgColor: 'bg-gray-200',
        textColor: 'text-gray-700',
        borderColor: 'border-gray-300',
        grayBg: 'bg-gray-100',
        grayText: 'text-gray-500',
        grayBorder: 'border-gray-200',
        clickedBorder: '!border-yellow-400',
        clickedScale: 'scale-110',
        clickedZ: 'z-20',
        clickedShadow: 'shadow-lg shadow-yellow-400/50',
    }), []);

    // 5. 圆形缩放配置（记忆化）
    const circleScaleConfig = useMemo(() => ({
        zIndex: 10,
        position: 'relative' as const,
    }), []);

    // 6. 优化的颜色信息处理函数（记忆化）
    const getEffectiveColor = useCallback((allColorInfo: any, colorPriority: ColorType[]) => {
        for (const color of colorPriority) {
            if (allColorInfo[color]) {
                return { ...allColorInfo[color]!, type: color };
            }
        }
        return null;
    }, []);

    // 7. Debug-3.2重构: 级别可见性检查函数，正确处理undefined级别
    const isLevelVisible = useCallback((visibility: any, level: number, colorType?: string, debugContext?: { coords?: string }) => {
        // Debug-3.2: 首先检查级别是否存在于AVAILABLE_LEVELS中
        if (colorType) {
            const availableLevels: Record<string, number[]> = {
                red: [1, 2, 3, 4],
                cyan: [1, 2, 3, 4],
                yellow: [1, 2, 3, 4],
                purple: [1, 2, 3, 4],
                orange: [1, 3, 4],
                green: [1, 3, 4],
                blue: [1, 3, 4],
                pink: [1, 3, 4],
            };

            const levelExists = availableLevels[colorType]?.includes(level) ?? false;
            if (!levelExists) {
                // Debug-3.2: 不存在的级别直接返回false，不再依赖默认值
                if (debugContext && (colorType === 'red' || process.env.NODE_ENV === 'development')) {
                    console.log(`⚠️  级别${level}在颜色${colorType}中不存在，返回false - ${debugContext.coords || 'unknown'}`);
                }
                return false;
            }
        }

        // Debug-3.2: 检查可见性设置，只有存在的级别才检查其可见性
        const levelKey = `showLevel${level}` as keyof typeof visibility;
        // 🔧 修复：更安全的布尔值检查，保持默认为true的行为
        const propertyValue = visibility?.[levelKey];
        const result = propertyValue !== false; // 只有明确为false才返回false，undefined或true都返回true

        // Debug Phase-1.2: 保留详细调试日志（条件性输出）
        if (debugContext && (colorType === 'red' || process.env.NODE_ENV === 'development')) {
            const hasProperty = levelKey in (visibility || {});

            console.log(`🔍 isLevelVisible调试 - ${debugContext.coords || 'unknown'}:`, {
                colorType: colorType,
                level: level,
                levelKey: levelKey,
                hasProperty: hasProperty,
                propertyValue: propertyValue,
                propertyType: typeof propertyValue,
                finalResult: result,
                levelExists: true, // 如果到达这里说明级别存在
                visibilityObject: visibility,
                修复说明: '现在只有明确为false的值才返回false，保持默认true行为'
            });

            // 特别关注红色1级、3级透明消失问题
            if (colorType === 'red' && (level === 1 || level === 3)) {
                console.warn(`🚨 红色${level}级可见性检查:`, {
                    level,
                    visibility,
                    levelKey,
                    hasProperty,
                    propertyValue,
                    propertyType: typeof propertyValue,
                    result,
                    message: result ? '✅ 应该可见' : '❌ 被隐藏',
                    修复前逻辑: 'propertyValue ?? true',
                    修复后逻辑: 'propertyValue !== false'
                });
            }
        }

        return result;
    }, []);

    // 8. 优化的CSS类构建函数（记忆化）
    const buildCellClasses = useCallback((
        baseClasses: string[], 
        cellShape: string, 
        bgColor: string, 
        textColor: string, 
        borderColor: string, 
        isClicked: boolean,
        defaultStyles: any
    ) => {
        const classes = [...baseClasses];

        if (cellShape === 'rounded') classes.push('rounded-md');
        else if (cellShape === 'circle') classes.push('rounded-full');

        classes.push(bgColor, textColor, borderColor, 'border');

        if (isClicked) {
            classes.push(
                defaultStyles.clickedBorder, 
                defaultStyles.clickedScale, 
                defaultStyles.clickedZ, 
                defaultStyles.clickedShadow
            );
        }

        return classes.join(' ');
    }, []);

    // 9. 优化的坐标键生成函数（记忆化）
    const getCellKey = useCallback((x: number, y: number) => `${x},${y}`, []);

    // 10. 安全的Set类型检查函数（记忆化）
    const ensureSetType = useCallback((clickedCells: any) => {
        return clickedCells instanceof Set 
            ? clickedCells 
            : new Set(Array.isArray(clickedCells) ? clickedCells : []);
    }, []);

    // 11. 新增：级别存在性检查函数（Debug Phase-1.2）
    const checkLevelExists = useCallback((colorType: string, level: number) => {
        // 这里需要导入AVAILABLE_LEVELS，暂时先用硬编码的逻辑
        const availableLevels: Record<string, number[]> = {
            red: [1, 2, 3, 4],
            cyan: [1, 2, 3, 4],
            yellow: [1, 2, 3, 4],
            purple: [1, 2, 3, 4],
            orange: [1, 3, 4],
            green: [1, 3, 4],
            blue: [1, 3, 4],
            pink: [1, 3, 4],
        };
        
        return availableLevels[colorType]?.includes(level) ?? false;
    }, []);

    // 12. 新增：调试特定坐标的渲染路径（Debug Phase-1.2）
    const debugCellRenderPath = useCallback((x: number, y: number, allColorInfo: any, debugLabel: string = '') => {
        const coords = `${x},${y}`;
        
        // 只对特定的调试坐标输出详细信息
        const isDebugCoord = (
            (x === 8 && y === 0) ||  // 红色1级
            (x === 4 && y === 0) ||  // 红色2级  
            (x === 2 && y === 0) ||  // 红色3级
            (x === 1 && y === 0)     // 红色4级
        );
        
        if (isDebugCoord || process.env.NODE_ENV === 'development') {
            console.log(`🎯 调试坐标 ${coords} ${debugLabel}:`, {
                x, y,
                allColorInfo,
                hasRed: !!allColorInfo.red,
                redInfo: allColorInfo.red,
                timestamp: new Date().toISOString()
            });
        }
        
        return coords;
    }, []);

    return {
        // 常量
        colorPriority,
        colorMap,
        baseClasses,
        defaultStyles,
        circleScaleConfig,
        
        // 优化函数
        getEffectiveColor,
        isLevelVisible,
        buildCellClasses,
        getCellKey,
        ensureSetType,
        
        // Debug Phase-1.2: 新增调试函数
        checkLevelExists,
        debugCellRenderPath,
    };
}; 