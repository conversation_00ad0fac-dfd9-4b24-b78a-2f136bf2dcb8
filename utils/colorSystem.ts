// 统一颜色检测系统 - 解决8种颜色函数重复的性能问题

export type ColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';

export interface ColorInfo {
  exists: boolean;
  level: number;
  group: number | null;
  colorType: ColorType;
}

export interface AllColorInfo {
  black?: { exists: boolean; letter: string };
  red?: ColorInfo;
  cyan?: ColorInfo;
  yellow?: ColorInfo;
  purple?: ColorInfo;
  orange?: ColorInfo;
  green?: ColorInfo;
  blue?: ColorInfo;
  pink?: ColorInfo;
}

// 颜色级别到CSS类的映射已迁移到constants/ui/css-mappings.ts

// 特殊坐标映射已迁移到constants/core/coordinates.ts

// 创建坐标索引 - 预计算所有坐标的颜色信息
export class ColorCoordinateIndex {
  private coordinateIndex: Map<string, ColorInfo> = new Map();
  private specialCoordinates: Map<string, string> = new Map();
  private allColorInfoCache: Map<string, AllColorInfo> = new Map(); // 新增：缓存机制
  private readonly cacheLimit = 1000; // 缓存限制，避免内存泄漏
  
  constructor(allColorCoordinates: Record<ColorType, any>, specialCoordinates?: Map<string, string>) {
    this.buildIndex(allColorCoordinates);
    if (specialCoordinates) {
      this.specialCoordinates = specialCoordinates;
    }
  }

  private buildIndex(allColorCoordinates: Record<ColorType, any>) {
    // 优化：使用更高效的批量处理
    const colorTypes = Object.keys(allColorCoordinates) as ColorType[];
    
    for (const colorType of colorTypes) {
      const coords = allColorCoordinates[colorType];
      
      // 批量处理每个颜色的所有级别
      for (const levelKey in coords) {
        if (Array.isArray(coords[levelKey])) {
          const level = parseInt(levelKey.replace('level', ''));
          const levelCoords = coords[levelKey];
          
          // 批量添加坐标到索引
          for (let i = 0; i < levelCoords.length; i++) {
            const coordGroup = levelCoords[i];
            const [x, y] = coordGroup.coords;
            const key = `${x},${y}`;
            
            this.coordinateIndex.set(key, {
              exists: true,
              level,
              group: coordGroup.group,
              colorType: colorType,
            });
          }
        }
      }
    }
    
    console.log(`ColorCoordinateIndex: 已索引 ${this.coordinateIndex.size} 个坐标`);
  }

  // O(1) 查找颜色信息
  getColorInfo(x: number, y: number, colorType: ColorType): ColorInfo {
    const key = `${x},${y}`;
    const info = this.coordinateIndex.get(key);
    
    if (info && info.colorType === colorType) {
      return info;
    }
    
    return {
      exists: false,
      level: 1,
      group: null,
      colorType,
    };
  }

  // 一次获取所有颜色信息 - 替代8次函数调用
  getAllColorInfo(x: number, y: number): AllColorInfo {
    const key = `${x},${y}`;
    
    // 检查缓存
    if (this.allColorInfoCache.has(key)) {
      return this.allColorInfoCache.get(key)!;
    }
    
    const result: AllColorInfo = {};
    
    // 检查黑色特殊坐标
    const specialLetter = this.specialCoordinates.get(key);
    if (specialLetter) {
      result.black = { exists: true, letter: specialLetter };
    }
    
    // 检查是否有任何颜色信息
    const info = this.coordinateIndex.get(key);
    if (info) {
      result[info.colorType] = info;
    }
    
    // 缓存结果（如果缓存未满）
    if (this.allColorInfoCache.size < this.cacheLimit) {
      this.allColorInfoCache.set(key, result);
    } else if (this.allColorInfoCache.size >= this.cacheLimit) {
      // LRU策略：清理最旧的缓存条目
      const firstKey = this.allColorInfoCache.keys().next().value;
      if (firstKey) {
        this.allColorInfoCache.delete(firstKey);
        this.allColorInfoCache.set(key, result);
      }
    }
    
    return result;
  }

  // 新增：批量查询优化（Phase 6.1性能提升）
  getAllColorInfoBatch(coordinates: Array<{ x: number; y: number }>): Map<string, AllColorInfo> {
    const result = new Map<string, AllColorInfo>();
    
    for (const coord of coordinates) {
      const key = `${coord.x},${coord.y}`;
      result.set(key, this.getAllColorInfo(coord.x, coord.y));
    }
    
    return result;
  }

  // 新增：缓存统计（开发模式使用）
  getCacheStats() {
    return {
      cacheSize: this.allColorInfoCache.size,
      cacheLimit: this.cacheLimit,
      cacheHitRate: this.allColorInfoCache.size > 0 ? 
        (this.allColorInfoCache.size / (this.allColorInfoCache.size + this.coordinateIndex.size)) * 100 : 0,
      totalIndexedCoordinates: this.coordinateIndex.size
    };
  }

  // 新增：清理缓存（内存管理）
  clearCache() {
    this.allColorInfoCache.clear();
  }

  // 新增：预热缓存（Phase 6.1性能优化）
  preWarmCache(hotCoordinates: Array<{ x: number; y: number }>) {
    console.log(`预热缓存：处理 ${hotCoordinates.length} 个热点坐标`);
    
    for (const coord of hotCoordinates) {
      this.getAllColorInfo(coord.x, coord.y);
    }
    
    console.log(`缓存预热完成，缓存大小：${this.allColorInfoCache.size}`);
  }
}

// 获取颜色对应的CSS类
export function getColorByLevel(colorType: ColorType, level: number, colorCssMap?: any): string {
  if (!colorCssMap) {
    // 如果没有提供 CSS 映射，返回默认值
    return 'bg-gray-300';
  }
  return colorCssMap[colorType]?.[`level${level}`] || colorCssMap[colorType]?.level1 || 'bg-gray-300';
}

// 全局索引实例优化 (Phase 6.1)
let globalColorIndex: ColorCoordinateIndex | null = null;

export function initializeColorIndex(allColorCoordinates: Record<ColorType, any>, specialCoordinates?: Map<string, string>) {
  globalColorIndex = new ColorCoordinateIndex(allColorCoordinates, specialCoordinates);
  
  // Phase 6.1: 自动预热热点区域缓存（中心区域更常被访问）
  const hotCoordinates = [];
  for (let x = 10; x <= 23; x++) {
    for (let y = 10; y <= 23; y++) {
      hotCoordinates.push({ x, y });
    }
  }
  globalColorIndex.preWarmCache(hotCoordinates);
  
  return globalColorIndex;
}

export function getGlobalColorIndex(): ColorCoordinateIndex | null {
  return globalColorIndex;
} 