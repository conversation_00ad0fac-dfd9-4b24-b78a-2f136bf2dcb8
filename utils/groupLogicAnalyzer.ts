/**
 * Debug Phase-2.1: 分组逻辑影响分析工具
 * 🎯 目标：分析撇捺分组vs横竖分组对级别显示的影响
 * 🔍 重点：检查showSpecificGroup筛选对级别显示的交叉影响
 */

import type { BasicColorType } from '../stores/basicDataStore';

// 分组逻辑分析结果类型
export interface GroupLogicAnalysisResult {
  colorType: BasicColorType;
  currentShowSpecificGroup: number | null;
  availableGroups: number[];
  colorGroups: number[];
  groupMode: 'pieNa' | 'zhuHeng' | 'mixed' | 'none';
  affectedLevels: {
    level: number;
    totalCoords: number;
    visibleCoords: number;
    hiddenByGroup: number;
    hiddenByLevel: number;
    visibleGroups: number[];
  }[];
  summary: {
    totalAffectedCoords: number;
    totalHiddenByGroup: number;
    totalHiddenByLevel: number;
    impactPercentage: number;
  };
}

export class GroupLogicAnalyzer {
  /**
   * 分析特定颜色的分组逻辑影响
   */
  static analyzeColorGroupLogic(
    colorType: BasicColorType,
    colorCoordinates: any,
    colorVisibility: any,
    showSpecificGroup: number | null,
    combinationSelectedGroups?: Set<number>
  ): GroupLogicAnalysisResult {
    console.log(`🔍 分析${colorType}颜色的分组逻辑影响...`);
    
    // 确定分组模式
    const groupMode = this.determineGroupMode(colorType);
    const availableGroups = this.getAvailableGroupsForColor(colorType);
    
    // 获取颜色的所有分组
    const colorGroups = this.extractColorGroups(colorCoordinates[colorType]);
    
    // 分析每个级别的影响
    const affectedLevels = [];
    let totalAffectedCoords = 0;
    let totalHiddenByGroup = 0;
    let totalHiddenByLevel = 0;
    
    const coordinates = colorCoordinates[colorType];
    const visibility = colorVisibility[colorType];
    
    for (const level of [1, 2, 3, 4]) {
      const levelKey = `level${level}` as keyof typeof coordinates;
      const levelCoords = coordinates[levelKey] || [];
      
      if (levelCoords.length === 0) continue;
      
      const visibilityKey = `showLevel${level}` as keyof typeof visibility;
      const isLevelVisible = visibility[visibilityKey] ?? true;
      
      let visibleCoords = 0;
      let hiddenByGroup = 0;
      let hiddenByLevel = 0;
      const visibleGroups = new Set<number>();
      
      for (const coord of levelCoords) {
        const group = coord.group;
        const isGroupVisible = showSpecificGroup === null || group === showSpecificGroup;
        
        if (!isLevelVisible) {
          hiddenByLevel++;
        } else if (!isGroupVisible) {
          hiddenByGroup++;
        } else {
          visibleCoords++;
          if (group) visibleGroups.add(group);
        }
      }
      
      affectedLevels.push({
        level,
        totalCoords: levelCoords.length,
        visibleCoords,
        hiddenByGroup,
        hiddenByLevel,
        visibleGroups: Array.from(visibleGroups).sort((a, b) => a - b),
      });
      
      totalAffectedCoords += levelCoords.length;
      totalHiddenByGroup += hiddenByGroup;
      totalHiddenByLevel += hiddenByLevel;
    }
    
    const impactPercentage = totalAffectedCoords > 0 
      ? ((totalHiddenByGroup + totalHiddenByLevel) / totalAffectedCoords) * 100 
      : 0;
    
    const result: GroupLogicAnalysisResult = {
      colorType,
      currentShowSpecificGroup: showSpecificGroup,
      availableGroups,
      colorGroups,
      groupMode,
      affectedLevels,
      summary: {
        totalAffectedCoords,
        totalHiddenByGroup,
        totalHiddenByLevel,
        impactPercentage,
      },
    };
    
    this.logAnalysisResult(result);
    return result;
  }

  /**
   * 分析全部8种颜色的分组逻辑影响
   */
  static analyzeAllColorsGroupLogic(
    colorCoordinates: any,
    colorVisibility: any,
    showSpecificGroup: number | null,
    combinationSelectedGroups?: Record<BasicColorType, Set<number>>
  ): GroupLogicAnalysisResult[] {
    console.log('🎯 开始全面分组逻辑影响分析...');
    
    const allColorTypes: BasicColorType[] = [
      'red', 'cyan', 'yellow', 'purple',
      'orange', 'green', 'blue', 'pink'
    ];
    
    const results = allColorTypes.map(colorType => 
      this.analyzeColorGroupLogic(
        colorType,
        colorCoordinates,
        colorVisibility,
        showSpecificGroup,
        combinationSelectedGroups?.[colorType]
      )
    );
    
    this.logSummaryAnalysis(results, showSpecificGroup);
    return results;
  }

  /**
   * 检查showSpecificGroup与分组系统的兼容性
   */
  static checkShowSpecificGroupCompatibility(
    showSpecificGroup: number | null
  ): {
    isValid: boolean;
    affectedColorTypes: BasicColorType[];
    groupMode: 'pieNa' | 'zhuHeng' | 'mixed' | 'invalid';
    warnings: string[];
  } {
    if (showSpecificGroup === null) {
      return {
        isValid: true,
        affectedColorTypes: [],
        groupMode: 'mixed',
        warnings: ['showSpecificGroup为null，所有分组都可见'],
      };
    }
    
    const warnings: string[] = [];
    let groupMode: 'pieNa' | 'zhuHeng' | 'mixed' | 'invalid' = 'invalid';
    let affectedColorTypes: BasicColorType[] = [];
    
    // 检查撇捺分组范围 (1-10)
    if (showSpecificGroup >= 1 && showSpecificGroup <= 10) {
      groupMode = 'pieNa';
      affectedColorTypes = ['red', 'cyan', 'yellow', 'purple'];
      warnings.push(`当前分组${showSpecificGroup}属于撇捺分组，只有红青黄紫颜色受影响`);
    }
    // 检查横竖分组范围 (11-44)
    else if (showSpecificGroup >= 11 && showSpecificGroup <= 44) {
      groupMode = 'zhuHeng';
      affectedColorTypes = ['orange', 'green', 'blue', 'pink'];
      warnings.push(`当前分组${showSpecificGroup}属于横竖分组，只有橙绿蓝粉颜色受影响`);
    }
    else {
      warnings.push(`无效的分组号${showSpecificGroup}，不属于任何已知分组范围`);
      return {
        isValid: false,
        affectedColorTypes: [],
        groupMode: 'invalid',
        warnings,
      };
    }
    
    return {
      isValid: true,
      affectedColorTypes,
      groupMode,
      warnings,
    };
  }

  /**
   * 确定颜色的分组模式
   */
  private static determineGroupMode(colorType: BasicColorType): 'pieNa' | 'zhuHeng' | 'mixed' | 'none' {
    if (['red', 'cyan', 'yellow', 'purple'].includes(colorType)) {
      return 'pieNa';
    }
    if (['orange', 'green', 'blue', 'pink'].includes(colorType)) {
      return 'zhuHeng';
    }
    return 'none';
  }

  /**
   * 获取颜色对应的可用分组范围
   */
  private static getAvailableGroupsForColor(colorType: BasicColorType): number[] {
    if (['red', 'cyan', 'yellow', 'purple'].includes(colorType)) {
      return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    }
    if (['orange', 'green', 'blue', 'pink'].includes(colorType)) {
      return [11, 12, 13, 14, 21, 22, 23, 24, 31, 32, 33, 34, 41, 42, 43, 44];
    }
    return [];
  }

  /**
   * 提取颜色坐标中的所有分组号
   */
  private static extractColorGroups(colorCoordinates: any): number[] {
    const groups = new Set<number>();
    
    for (const level of ['level1', 'level2', 'level3', 'level4']) {
      const levelCoords = colorCoordinates[level] || [];
      for (const coord of levelCoords) {
        if (coord.group) {
          groups.add(coord.group);
        }
      }
    }
    
    return Array.from(groups).sort((a, b) => a - b);
  }

  /**
   * 记录分析结果
   */
  private static logAnalysisResult(result: GroupLogicAnalysisResult) {
    console.log(`📊 ${result.colorType}颜色分组逻辑分析结果:`);
    console.log(`   分组模式: ${result.groupMode}`);
    console.log(`   当前showSpecificGroup: ${result.currentShowSpecificGroup}`);
    console.log(`   颜色包含的分组: [${result.colorGroups.join(', ')}]`);
    console.log(`   总体影响: ${result.summary.impactPercentage.toFixed(1)}%的坐标被隐藏`);
    
    if (result.summary.totalHiddenByGroup > 0) {
      console.warn(`   ⚠️  ${result.summary.totalHiddenByGroup}个坐标因分组筛选被隐藏`);
    }
    
    if (result.summary.totalHiddenByLevel > 0) {
      console.warn(`   ⚠️  ${result.summary.totalHiddenByLevel}个坐标因级别设置被隐藏`);
    }
    
    result.affectedLevels.forEach(levelInfo => {
      if (levelInfo.totalCoords > 0) {
        console.log(`   Level ${levelInfo.level}: ${levelInfo.visibleCoords}/${levelInfo.totalCoords} 可见 (分组: [${levelInfo.visibleGroups.join(', ')}])`);
      }
    });
  }

  /**
   * 记录汇总分析
   */
  private static logSummaryAnalysis(results: GroupLogicAnalysisResult[], showSpecificGroup: number | null) {
    console.log('\n🎯 分组逻辑影响分析汇总:');
    console.log(`当前showSpecificGroup: ${showSpecificGroup}`);
    
    // 兼容性检查
    const compatibility = this.checkShowSpecificGroupCompatibility(showSpecificGroup);
    console.log(`兼容性检查: ${compatibility.isValid ? '✅ 有效' : '❌ 无效'}`);
    compatibility.warnings.forEach(warning => console.warn(`  ⚠️  ${warning}`));
    
    // 受影响的颜色统计
    const affectedColors = results.filter(r => r.summary.totalHiddenByGroup > 0);
    if (affectedColors.length > 0) {
      console.log(`受分组筛选影响的颜色: ${affectedColors.map(r => r.colorType).join(', ')}`);
    }
    
    // 总体统计
    const totalCoords = results.reduce((sum, r) => sum + r.summary.totalAffectedCoords, 0);
    const totalHiddenByGroup = results.reduce((sum, r) => sum + r.summary.totalHiddenByGroup, 0);
    const totalHiddenByLevel = results.reduce((sum, r) => sum + r.summary.totalHiddenByLevel, 0);
    
    console.log(`总体影响统计:`);
    console.log(`  总坐标数: ${totalCoords}`);
    console.log(`  分组隐藏: ${totalHiddenByGroup} (${(totalHiddenByGroup/totalCoords*100).toFixed(1)}%)`);
    console.log(`  级别隐藏: ${totalHiddenByLevel} (${(totalHiddenByLevel/totalCoords*100).toFixed(1)}%)`);
  }
}

// 便捷函数
export const analyzeGroupLogic = (
  colorCoordinates: any,
  colorVisibility: any,
  showSpecificGroup: number | null,
  combinationSelectedGroups?: Record<BasicColorType, Set<number>>
) => {
  return GroupLogicAnalyzer.analyzeAllColorsGroupLogic(
    colorCoordinates,
    colorVisibility,
    showSpecificGroup,
    combinationSelectedGroups
  );
};

export const checkGroupCompatibility = (showSpecificGroup: number | null) => {
  return GroupLogicAnalyzer.checkShowSpecificGroupCompatibility(showSpecificGroup);
}; 