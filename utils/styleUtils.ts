import { type CellData } from '@/types/grid';
import { type ColorType } from '@/types/color';

/**
 * 合并类名的工具函数 (简化版的clsx)
 */
export const cn = (...inputs: Array<string | undefined | null | false>): string => {
  return inputs.filter(Boolean).join(' ');
};

/**
 * 生成网格容器样式
 */
export const getGridContainerStyle = (
  fontSize: number,
  matrixMargin: number,
  cellShape: string
): string => {
  return cn(
    'grid',
    'grid-cols-33',
    'gap-1',
    'w-full',
    'max-w-4xl',
    'mx-auto',
    `text-${fontSize}px`,
    `p-${matrixMargin}`,
    `cells-${cellShape}`
  );
};

/**
 * 生成单元格基础样式
 */
export const getCellBaseStyle = (
  cellShape: string,
  backgroundColor: string
): string => {
  return cn(
    cellShape === 'circle' ? 'rounded-full' : cellShape === 'rounded' ? 'rounded-md' : '',
    backgroundColor,
    'text-white',
    'text-center',
    'flex',
    'items-center',
    'justify-center',
    'transition-all',
    'duration-200',
    'hover:scale-105',
    'cursor-pointer',
    'border',
    'border-gray-200'
  );
};

// 颜色背景样式映射已迁移到constants/ui/css-mappings.ts 