/**
 * 按钮工具函数 - 按钮样式生成和组合逻辑
 * 🎯 职责：按钮样式生成、状态组合、颜色变体等工具函数
 * 📦 数据源：原styleStore.ts中的按钮样式工具函数
 * ✅ Phase 4.7.1: 职责分离 - 工具函数与状态管理分离
 */

import {
  ButtonVariant,
  ButtonSize,
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
  BUTTON_STYLES,
  TAB_STYLES,
  GRID_STYLES,
} from '../constants/styles';
import { BasicColorType } from '../constants/colors';

// 高级按钮样式生成器
export const getAdvancedButtonStyle = (
  variant: ButtonVariant, 
  size: ButtonSize, 
  extraClasses: string = ''
): string => {
  const sizeStyle = SIZE_STYLES[size];
  const variantStyle = VARIANT_STYLES[variant];
  return [BASE_BUTTON_STYLES, sizeStyle, variantStyle, extraClasses].filter(Boolean).join(' ');
};

// 激活状态按钮样式
export const getActiveButtonStyle = (
  isActive: boolean, 
  size: ButtonSize = 'sm', 
  extraClasses: string = ''
): string => {
  return getAdvancedButtonStyle(isActive ? 'active' : 'inactive', size, extraClasses);
};

// 颜色按钮样式
export const getColorButtonStyle = (
  isSelected: boolean, 
  colorClass: string = '', 
  size: ButtonSize = 'sm'
): string => {
  if (isSelected && colorClass) {
    return getAdvancedButtonStyle('primary', size, `${colorClass} text-white border-transparent`);
  }
  return getAdvancedButtonStyle('inactive', size);
};

// 网格按钮样式
export const getGridButtonStyle = (
  isSelected: boolean, 
  color: BasicColorType | 'gray' = 'gray', 
  size: ButtonSize = 'xs'
): string => {
  const colorVariants: Record<BasicColorType | 'gray', string> = {
    red: 'bg-red-600 border-red-500',
    blue: 'bg-blue-600 border-blue-500', 
    green: 'bg-green-600 border-green-500',
    yellow: 'bg-yellow-600 border-yellow-500',
    purple: 'bg-purple-600 border-purple-500',
    orange: 'bg-orange-600 border-orange-500',
    cyan: 'bg-cyan-600 border-cyan-500',
    pink: 'bg-pink-600 border-pink-500',
    black: 'bg-black border-gray-500',
    gray: 'bg-gray-600 border-gray-500',
  };
  
  if (isSelected) {
    return getAdvancedButtonStyle('primary', size, `${colorVariants[color]} text-white`);
  }
  return getAdvancedButtonStyle('inactive', size);
};

// 模式按钮样式
export const getModeButtonStyle = (
  isCurrentMode: boolean, 
  size: ButtonSize = 'sm'
): string => {
  return getAdvancedButtonStyle(isCurrentMode ? 'primary' : 'inactive', size);
};

// 危险按钮样式
export const getDangerButtonStyle = (
  size: ButtonSize = 'sm', 
  extraClasses: string = ''
): string => {
  return getAdvancedButtonStyle('danger', size, extraClasses);
};

// 成功按钮样式
export const getSuccessButtonStyle = (
  size: ButtonSize = 'sm', 
  extraClasses: string = ''
): string => {
  return getAdvancedButtonStyle('success', size, extraClasses);
};

// 简单按钮样式访问器（保持向后兼容）
export const getButtonStyle = (variant: keyof typeof BUTTON_STYLES): string => {
  return BUTTON_STYLES[variant];
};

// Tab样式生成器
export const getTabStyle = (active: boolean): string => {
  return active ? TAB_STYLES.active : TAB_STYLES.inactive;
};

// 网格样式生成器
export const getGridStyle = (cols: 2 | 3 | 4 | 5): string => {
  return GRID_STYLES[`cols${cols}` as keyof typeof GRID_STYLES];
}; 