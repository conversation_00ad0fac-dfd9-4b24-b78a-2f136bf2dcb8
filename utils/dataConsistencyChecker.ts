/**
 * 数据一致性诊断工具
 * 🎯 Debug Phase-1.1: 深度检查颜色级别数据的一致性问题
 * 📊 检查 AVAILABLE_LEVELS ↔ DEFAULT_COLOR_VISIBILITY ↔ 实际坐标数据 三者一致性
 * 🚨 为颜色级别显示异常问题提供详细诊断报告
 */

import { AVAILABLE_LEVELS, DEFAULT_COLOR_VISIBILITY, type BasicColorType, type ColorVisibility } from '../stores/basicDataStore';

// 诊断结果接口
interface ConsistencyIssue {
  type: 'missing_level' | 'extra_level' | 'undefined_property' | 'data_mismatch';
  color: BasicColorType;
  level?: number;
  expected?: any;
  actual?: any;
  description: string;
  severity: 'error' | 'warning' | 'info';
}

interface ConsistencyReport {
  totalIssues: number;
  errors: ConsistencyIssue[];
  warnings: ConsistencyIssue[];
  infos: ConsistencyIssue[];
  summary: {
    affectedColors: BasicColorType[];
    consistentColors: BasicColorType[];
    mostProblematicColor: BasicColorType | null;
  };
}

export class DataConsistencyChecker {
  private issues: ConsistencyIssue[] = [];

  /**
   * 执行完整的数据一致性检查
   */
  public checkDataConsistency(): ConsistencyReport {
    console.log('🔍 开始数据一致性检查...');
    
    this.issues = []; // 重置问题列表
    
    // 1. 检查 AVAILABLE_LEVELS 与 DEFAULT_COLOR_VISIBILITY 的一致性
    this.checkLevelsVisibilityConsistency();
    
    // 2. 检查颜色坐标数据的完整性（如果可以访问）
    this.checkCoordinateDataConsistency();
    
    // 3. 检查级别属性的定义完整性
    this.checkLevelPropertyDefinitions();
    
    // 生成报告
    const report = this.generateReport();
    this.logReport(report);
    
    return report;
  }

  /**
   * 检查 AVAILABLE_LEVELS 和 DEFAULT_COLOR_VISIBILITY 的一致性
   */
  private checkLevelsVisibilityConsistency(): void {
    console.log('📋 检查级别-可见性一致性...');
    
    const colorTypes = Object.keys(AVAILABLE_LEVELS) as BasicColorType[];
    
    for (const colorType of colorTypes) {
      const availableLevels = AVAILABLE_LEVELS[colorType];
      const defaultVisibility = DEFAULT_COLOR_VISIBILITY[colorType];
      
      // 检查每个可用级别是否在可见性配置中有对应属性
      for (const level of availableLevels) {
        const levelKey = `showLevel${level}` as keyof ColorVisibility;
        
        if (!(levelKey in defaultVisibility)) {
          this.addIssue({
            type: 'missing_level',
            color: colorType,
            level,
            description: `颜色 ${colorType} 的级别 ${level} 在 AVAILABLE_LEVELS 中存在，但在 DEFAULT_COLOR_VISIBILITY 中缺少 ${levelKey} 属性`,
            severity: 'error'
          });
        } else if (defaultVisibility[levelKey] === undefined) {
          this.addIssue({
            type: 'undefined_property',
            color: colorType,
            level,
            description: `颜色 ${colorType} 的 ${levelKey} 属性存在但值为 undefined`,
            severity: 'warning'
          });
        }
      }
      
      // 检查可见性配置中是否有多余的级别属性
      for (let level = 1; level <= 4; level++) {
        const levelKey = `showLevel${level}` as keyof ColorVisibility;
        
        if (levelKey in defaultVisibility && !availableLevels.includes(level)) {
          this.addIssue({
            type: 'extra_level',
            color: colorType,
            level,
            description: `颜色 ${colorType} 的 ${levelKey} 属性在 DEFAULT_COLOR_VISIBILITY 中存在，但级别 ${level} 不在 AVAILABLE_LEVELS 中`,
            severity: 'warning'
          });
        }
      }
    }
  }

  /**
   * 检查颜色坐标数据的一致性（基础检查）
   */
  private checkCoordinateDataConsistency(): void {
    console.log('🗺️  检查坐标数据一致性...');
    
    // 这里我们先检查基础的数据结构
    // 实际的坐标数据检查需要在运行时进行
    const colorTypes = Object.keys(AVAILABLE_LEVELS) as BasicColorType[];
    
    for (const colorType of colorTypes) {
      const availableLevels = AVAILABLE_LEVELS[colorType];
      
      // 检查特殊的级别缺失情况
      if (colorType === 'orange' || colorType === 'green' || colorType === 'blue' || colorType === 'pink') {
        if (availableLevels.includes(2)) {
          this.addIssue({
            type: 'data_mismatch',
            color: colorType,
            level: 2,
            expected: 'level2 不应存在',
            actual: 'level2 在 AVAILABLE_LEVELS 中存在',
            description: `颜色 ${colorType} 按设计不应该有 level2，但在 AVAILABLE_LEVELS 中包含了级别 2`,
            severity: 'error'
          });
        }
      }
    }
  }

  /**
   * 检查级别属性的定义完整性
   */
  private checkLevelPropertyDefinitions(): void {
    console.log('🔧 检查级别属性定义完整性...');
    
    const colorTypes = Object.keys(AVAILABLE_LEVELS) as BasicColorType[];
    
    for (const colorType of colorTypes) {
      const availableLevels = AVAILABLE_LEVELS[colorType];
      const defaultVisibility = DEFAULT_COLOR_VISIBILITY[colorType];
      
      // 统计每种颜色的级别覆盖情况
      const definedLevels: number[] = [];
      const undefinedLevels: number[] = [];
      
      for (let level = 1; level <= 4; level++) {
        const levelKey = `showLevel${level}` as keyof ColorVisibility;
        
        if (levelKey in defaultVisibility && defaultVisibility[levelKey] !== undefined) {
          definedLevels.push(level);
        } else if (availableLevels.includes(level)) {
          undefinedLevels.push(level);
        }
      }
      
      if (undefinedLevels.length > 0) {
        this.addIssue({
          type: 'undefined_property',
          color: colorType,
          description: `颜色 ${colorType} 有 ${undefinedLevels.length} 个级别未正确定义: [${undefinedLevels.join(', ')}]`,
          severity: 'error'
        });
      }
      
      // 记录完整定义的颜色（用于对比）
      if (undefinedLevels.length === 0 && definedLevels.length === availableLevels.length) {
        this.addIssue({
          type: 'data_mismatch',
          color: colorType,
          description: `颜色 ${colorType} 的级别定义完全一致 ✅ (${definedLevels.length}个级别)`,
          severity: 'info'
        });
      }
    }
  }

  /**
   * 添加问题到列表
   */
  private addIssue(issue: ConsistencyIssue): void {
    this.issues.push(issue);
  }

  /**
   * 生成诊断报告
   */
  private generateReport(): ConsistencyReport {
    const errors = this.issues.filter(issue => issue.severity === 'error');
    const warnings = this.issues.filter(issue => issue.severity === 'warning');
    const infos = this.issues.filter(issue => issue.severity === 'info');
    
    const affectedColors = Array.from(new Set(
      this.issues
        .filter(issue => issue.severity !== 'info')
        .map(issue => issue.color)
    ));
    
    const allColors = Object.keys(AVAILABLE_LEVELS) as BasicColorType[];
    const consistentColors = allColors.filter(color => !affectedColors.includes(color));
    
    // 找出问题最多的颜色
    const colorIssueCounts = affectedColors.reduce((acc, color) => {
      acc[color] = this.issues.filter(issue => 
        issue.color === color && issue.severity !== 'info'
      ).length;
      return acc;
    }, {} as Record<BasicColorType, number>);
    
    const mostProblematicColor = affectedColors.reduce((worst, color) => 
      !worst || (colorIssueCounts[color] > colorIssueCounts[worst]) ? color : worst
    , null as BasicColorType | null);

    return {
      totalIssues: this.issues.length,
      errors,
      warnings,
      infos,
      summary: {
        affectedColors,
        consistentColors,
        mostProblematicColor
      }
    };
  }

  /**
   * 在控制台输出详细报告
   */
  private logReport(report: ConsistencyReport): void {
    console.log('\n📊 数据一致性检查报告');
    console.log('='.repeat(50));
    
    // 总览
    console.log(`🔢 总问题数: ${report.totalIssues}`);
    console.log(`❌ 错误: ${report.errors.length}`);
    console.log(`⚠️  警告: ${report.warnings.length}`);
    console.log(`ℹ️  信息: ${report.infos.length}`);
    
    // 受影响的颜色
    console.log(`\n🎨 受影响的颜色 (${report.summary.affectedColors.length}/${Object.keys(AVAILABLE_LEVELS).length}):`);
    if (report.summary.affectedColors.length > 0) {
      console.log(`   ${report.summary.affectedColors.join(', ')}`);
    } else {
      console.log('   无');
    }
    
    // 一致的颜色
    console.log(`\n✅ 数据一致的颜色:`);
    if (report.summary.consistentColors.length > 0) {
      console.log(`   ${report.summary.consistentColors.join(', ')}`);
    } else {
      console.log('   无');
    }
    
    // 最有问题的颜色
    if (report.summary.mostProblematicColor) {
      console.log(`\n🚨 问题最多的颜色: ${report.summary.mostProblematicColor}`);
    }
    
    // 详细问题列表
    if (report.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      report.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. [${error.color}] ${error.description}`);
      });
    }
    
    if (report.warnings.length > 0) {
      console.log('\n⚠️  警告详情:');
      report.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. [${warning.color}] ${warning.description}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
    
    // 修复建议
    this.logFixSuggestions(report);
  }

  /**
   * 输出修复建议
   */
  private logFixSuggestions(report: ConsistencyReport): void {
    console.log('💡 修复建议:');
    
    if (report.errors.length > 0) {
      console.log('\n1. 立即修复的关键问题:');
      
      const missingLevelErrors = report.errors.filter(e => e.type === 'missing_level');
      if (missingLevelErrors.length > 0) {
        console.log('   - 为缺失的级别添加对应的可见性属性');
        missingLevelErrors.forEach(error => {
          console.log(`     ${error.color}: 添加 showLevel${error.level} 属性`);
        });
      }
      
      const dataMismatchErrors = report.errors.filter(e => e.type === 'data_mismatch');
      if (dataMismatchErrors.length > 0) {
        console.log('   - 修正数据不匹配问题');
        dataMismatchErrors.forEach(error => {
          console.log(`     ${error.color}: ${error.description}`);
        });
      }
    }
    
    if (report.warnings.length > 0) {
      console.log('\n2. 建议优化的问题:');
      
      const extraLevelWarnings = report.warnings.filter(w => w.type === 'extra_level');
      if (extraLevelWarnings.length > 0) {
        console.log('   - 移除多余的级别属性或在 AVAILABLE_LEVELS 中添加对应级别');
      }
      
      const undefinedPropertyWarnings = report.warnings.filter(w => w.type === 'undefined_property');
      if (undefinedPropertyWarnings.length > 0) {
        console.log('   - 为 undefined 的属性设置明确的 boolean 值');
      }
    }
    
    console.log('\n3. 推荐的数据模式:');
    console.log('   - 使用 AVAILABLE_LEVELS 作为唯一的级别定义来源');
    console.log('   - 根据 AVAILABLE_LEVELS 自动生成 DEFAULT_COLOR_VISIBILITY');
    console.log('   - 建立数据Schema验证机制，防止未来的不一致');
  }

  /**
   * 获取特定颜色的详细信息
   */
  public getColorDetails(colorType: BasicColorType): {
    availableLevels: number[];
    definedLevels: number[];
    missingLevels: number[];
    extraLevels: number[];
    issues: ConsistencyIssue[];
  } {
    const availableLevels = AVAILABLE_LEVELS[colorType];
    const defaultVisibility = DEFAULT_COLOR_VISIBILITY[colorType];
    
    const definedLevels: number[] = [];
    const missingLevels: number[] = [];
    const extraLevels: number[] = [];
    
    // 检查已定义的级别
    for (let level = 1; level <= 4; level++) {
      const levelKey = `showLevel${level}` as keyof ColorVisibility;
      const isAvailable = availableLevels.includes(level);
      const isDefined = levelKey in defaultVisibility && defaultVisibility[levelKey] !== undefined;
      
      if (isDefined) {
        definedLevels.push(level);
        if (!isAvailable) {
          extraLevels.push(level);
        }
      } else if (isAvailable) {
        missingLevels.push(level);
      }
    }
    
    const colorIssues = this.issues.filter(issue => issue.color === colorType);
    
    return {
      availableLevels,
      definedLevels,
      missingLevels,
      extraLevels,
      issues: colorIssues
    };
  }
}

// 导出便捷函数
export function runDataConsistencyCheck(): ConsistencyReport {
  const checker = new DataConsistencyChecker();
  return checker.checkDataConsistency();
}

export function checkSpecificColor(colorType: BasicColorType) {
  const checker = new DataConsistencyChecker();
  checker.checkDataConsistency(); // 先运行完整检查
  return checker.getColorDetails(colorType);
}

// 调试用的预期发现验证
export function validateExpectedIssues(): boolean {
  console.log('🔍 验证预期问题...');
  
  const report = runDataConsistencyCheck();
  
  // 检查是否发现了预期的橙色等颜色的level2问题
  const orangeLevel2Issue = report.errors.find(
    issue => issue.color === 'orange' && issue.level === 2 && issue.type === 'missing_level'
  );
  
  if (orangeLevel2Issue) {
    console.log('✅ 发现了预期的橙色level2问题');
    return true;
  } else {
    console.log('❌ 未发现预期的橙色level2问题');
    return false;
  }
} 