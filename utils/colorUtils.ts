/**
 * 颜色工具函数 - 颜色处理和查询逻辑
 * 🎯 职责：颜色查询、名称转换、优先级计算等工具函数
 * 📦 数据源：原styleStore.ts中的颜色工具函数
 * ✅ Phase 4.7.1: 职责分离 - 工具函数与状态管理分离
 */

import {
  ColorType,
  BasicColorType,
  COLOR_CSS_MAP,
  BLACK_CSS_MAP,
  COLOR_NAMES,
  COLOR_SHORT_NAMES,
  COLOR_NUMBER_MAP,
  COLOR_PRIORITY_ORDER,
} from '../constants/colors';

// 获取颜色CSS样式
export const getColorCSS = (colorType: ColorType, styleType: string): string => {
  return COLOR_CSS_MAP[colorType]?.[styleType] || '';
};

// 获取颜色CSS映射
export const getColorCSSMap = (colorType: ColorType): typeof COLOR_CSS_MAP[ColorType] | undefined => {
  return COLOR_CSS_MAP[colorType];
};

// 获取黑色CSS样式
export const getBlackCSS = (styleType: string): string => {
  return BLACK_CSS_MAP[styleType as keyof typeof BLACK_CSS_MAP] || '';
};

// 获取颜色名称
export const getColorName = (colorType: ColorType | 'black'): string => {
  return COLOR_NAMES[colorType];
};

// 获取颜色显示名称（支持完整/简短格式）
export const getColorDisplayName = (
  colorType: BasicColorType, 
  format: 'full' | 'short' = 'full'
): string => {
  return format === 'short' ? COLOR_SHORT_NAMES[colorType] : COLOR_NAMES[colorType];
};

// 获取颜色数字编码
export const getColorNumber = (colorType: Exclude<BasicColorType, 'black'>): string => {
  return COLOR_NUMBER_MAP[colorType] || '';
};

// 获取颜色优先级
export const getColorPriority = (colorType: BasicColorType): number => {
  return COLOR_PRIORITY_ORDER.indexOf(colorType);
};

// 根据级别获取单元格样式
export const getCellStyle = (colorType: ColorType, level: number, clicked = false): string => {
  const baseStyles = getColorCSS(colorType, `level${level}`);
  const clickedStyles = clicked ? 'scale-110 z-20 shadow-lg' : '';
  return `${baseStyles} ${clickedStyles} transition-all duration-200`.trim();
};

// Tab样式生成器（用于颜色选择等）
export const getTabStyle = (tabKey: string, activeTab: string): string => {
  const isActive = tabKey === activeTab;
  return isActive ? 'bg-white text-gray-800 border-gray-400 shadow-sm' : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200';
}; 