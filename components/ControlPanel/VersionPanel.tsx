import React, { memo, useCallback } from 'react';
import { VersionPanelProps } from './types';

/**
 * 版本管理面板组件
 * 🎯 核心价值：统一管理版本切换、保存、导入导出等功能
 * ⚡ 性能优化：使用memo和useCallback保持性能
 */
export const VersionPanel = memo<VersionPanelProps>((props) => {
  const {
    currentVersion,
    onVersionChange,
    versions,
    onSaveVersion,
    onDeleteVersion,
    onExportData,
    onImportData
  } = props;

  const handleVersionChange = useCallback((versionId: string) => {
    // 版本切换必须保持 < 100ms
    onVersionChange(versionId);
  }, [onVersionChange]);

  const handleSaveVersion = useCallback(() => {
    onSaveVersion();
  }, [onSaveVersion]);

  const handleDeleteVersion = useCallback((versionId: string) => {
    onDeleteVersion(versionId);
  }, [onDeleteVersion]);

  const handleExportData = useCallback(() => {
    onExportData();
  }, [onExportData]);

  const handleImportData = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onImportData(event);
  }, [onImportData]);

  return (
    <div className="space-y-3">
      {/* 当前版本显示 */}
      <div>
        <label className="block text-sm font-medium text-gray-600 mb-2">
          当前版本
        </label>
        <div className="text-sm bg-blue-50 border border-blue-200 rounded px-3 py-2">
          {currentVersion}
        </div>
      </div>

      {/* 版本选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-600 mb-2">
          版本切换
        </label>
        <select
          value={currentVersion}
          onChange={(e) => handleVersionChange(e.target.value)}
          className="w-full text-sm border border-gray-300 rounded px-3 py-2 bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
        >
          {versions.map(version => (
            <option key={version.id} value={version.id}>
              {version.name}
            </option>
          ))}
        </select>
      </div>

      {/* 版本操作 */}
      <div className="space-y-2">
        <button
          onClick={handleSaveVersion}
          className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          保存当前版本
        </button>
        
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={handleExportData}
            className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            导出
          </button>
          
          <label className="px-3 py-2 text-sm bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors cursor-pointer text-center">
            导入
            <input
              type="file"
              accept=".json"
              onChange={handleImportData}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* 版本列表 */}
      {versions.length > 1 && (
        <div>
          <label className="block text-sm font-medium text-gray-600 mb-2">
            版本管理 ({versions.length})
          </label>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {versions.map(version => (
              <div
                key={version.id}
                className={`flex items-center justify-between p-2 text-xs rounded border ${
                  currentVersion === version.id
                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-600'
                }`}
              >
                <div className="flex-1">
                  <div className="font-medium">{version.name}</div>
                  <div className="text-gray-500">{version.description}</div>
                </div>
                {currentVersion !== version.id && (
                  <button
                    onClick={() => handleDeleteVersion(version.id)}
                    className="ml-2 text-red-500 hover:text-red-700"
                    title="删除版本"
                  >
                    ✕
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

VersionPanel.displayName = 'VersionPanel'; 