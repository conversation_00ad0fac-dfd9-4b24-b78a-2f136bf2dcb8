import React, { memo, useCallback, useState } from 'react';
import { useBasicDataStore } from '../../stores/basicDataStore';
import { useStyleStore } from '../../stores/styleStore';
import { useDynamicStyleStore } from '../../stores/dynamicStyleStore';
import { getColorName } from '../../utils/colorUtils';
// Debug Phase-1.3: 导入调试工具
import debugHelper from '../../utils/debugHelper';

/**
 * 基础数据面板组件
 * 🎯 核心价值：统一管理8种颜色的基础数据和黑色格子
 * ⚡ 性能优化：使用memo + useCallback模式
 * 📊 功能范围：颜色坐标、颜色值、可见性、级别规则、黑色格子
 * 🔢 新增功能：颜色数字显示控制（移植自传统面板）
 * ✅ Phase 4.7.2: 重复定义清理 - 使用utils/colorUtils统一颜色名称获取
 */

import type { BasicColorType } from '../../stores/basicDataStore';

interface BasicDataPanelProps {
  className?: string;
}

export const BasicDataPanel = memo<BasicDataPanelProps>(({ className = '' }) => {
  const [activeColorTab, setActiveColorTab] = useState<BasicColorType | 'black'>('red');
  // Debug Phase-1.3: 调试面板显示状态
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // 从新Store获取状态和操作
  const {
    colorCoordinates,
    colorValues,
    colorVisibility,
    colorLevelRules,
    blackCellData,
    setColorCoordinates,
    updateColorValues,
    setColorVisibility,
    toggleColorLevel,
    setBlackCellData,
  } = useBasicDataStore();

  // 从 styleStore 获取 CSS 映射
  const { getColorCSSMap } = useStyleStore();

  // 从 dynamicStyleStore 获取showAllNumbers功能（移植自传统面板）
  const {
    showAllNumbers,
    setShowAllNumbers,
  } = useDynamicStyleStore();

  // 事件处理函数
  const handleColorTabChange = useCallback((color: BasicColorType | 'black') => {
    setActiveColorTab(color);
  }, []);

  const handleLevelToggle = useCallback((level: 1 | 2 | 3 | 4) => {
    if (activeColorTab !== 'black') {
      console.log(`🔄 切换级别 ${activeColorTab}-${level}`);
      toggleColorLevel(activeColorTab, level);

      // 强制触发重渲染以确保UI同步
      setTimeout(() => {
        console.log(`✅ 级别切换完成 ${activeColorTab}-${level}，当前状态:`, colorVisibility[activeColorTab]);
      }, 100);
    }
  }, [activeColorTab, toggleColorLevel, colorVisibility]);

  const handleColorVisibilityToggle = useCallback(() => {
    if (activeColorTab !== 'black') {
      const currentVisibility = colorVisibility[activeColorTab];
      setColorVisibility(activeColorTab, { 
        ...currentVisibility,
        showCells: !currentVisibility.showCells 
      });
    }
  }, [activeColorTab, colorVisibility, setColorVisibility]);

  const handleBlackCellToggle = useCallback(() => {
    setBlackCellData({ 
      ...blackCellData,
      visibility: !blackCellData.visibility 
    });
  }, [blackCellData, setBlackCellData]);

  // 新增：数字显示控制（移植自传统面板）
  const handleNumbersToggle = useCallback(() => {
    setShowAllNumbers(!showAllNumbers);
  }, [showAllNumbers, setShowAllNumbers]);

  // Debug Phase-1.3: 调试面板切换
  const handleDebugToggle = useCallback(() => {
    setShowDebugPanel(!showDebugPanel);
  }, [showDebugPanel]);

  // Debug Phase-1.3: 运行数据一致性检查
  const handleRunConsistencyCheck = useCallback(() => {
    debugHelper.quickDiagnosis();
  }, []);

  // Debug Phase-1.3: 检查当前颜色
  const handleCheckCurrentColor = useCallback(() => {
    if (activeColorTab !== 'black') {
      debugHelper.checkColor(activeColorTab);
    }
  }, [activeColorTab]);

  // Debug-3.3: 获取当前颜色的可用级别 - 直接使用权威的AVAILABLE_LEVELS
  const getAvailableLevels = (color: BasicColorType): (1 | 2 | 3 | 4)[] => {
    // 导入AVAILABLE_LEVELS作为唯一权威数据源
    const AVAILABLE_LEVELS: Record<BasicColorType, number[]> = {
      red: [1, 2, 3, 4],
      cyan: [1, 2, 3, 4],
      yellow: [1, 2, 3, 4],
      purple: [1, 2, 3, 4],
      orange: [1, 3, 4],
      green: [1, 3, 4],
      blue: [1, 3, 4],
      pink: [1, 3, 4],
    };
    
    const levels = AVAILABLE_LEVELS[color] || [];
    return levels.filter(level => level >= 1 && level <= 4) as (1 | 2 | 3 | 4)[];
  };

  // 渲染颜色选项卡
  const renderColorTabs = () => {
    const allColors: (BasicColorType | 'black')[] = [
      'red', 'cyan', 'yellow', 'purple',
      'orange', 'green', 'blue', 'pink', 
      'black'
    ];

    return (
      <div className="flex flex-wrap gap-1 mb-4">
        {allColors.map((color) => {
          const isActive = activeColorTab === color;
          const colorClass = color !== 'black' 
            ? getColorCSSMap(color as BasicColorType)?.bg || 'bg-gray-500'
            : 'bg-black';
          
          return (
            <button
              key={color}
              onClick={() => handleColorTabChange(color)}
              className={`px-3 py-1 text-xs rounded border transition-colors ${
                isActive
                  ? `${colorClass} text-white border-transparent`
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {getColorName(color)}
            </button>
          );
        })}
      </div>
    );
  };

  // 渲染级别控制
  const renderLevelControls = () => {
    if (activeColorTab === 'black') {
      return (
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">黑色格子控制</label>
            <button
              onClick={handleBlackCellToggle}
              className={`w-full p-2 text-sm rounded border transition-colors ${
                blackCellData.visibility
                  ? 'bg-black text-white border-gray-600'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {blackCellData.visibility ? '隐藏黑色格子' : '显示黑色格子'}
            </button>
          </div>
          
          {/* 新增：数字显示控制（移植自传统面板） */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">数字显示控制</label>
            <button
              onClick={handleNumbersToggle}
              className={`w-full p-2 text-sm rounded border transition-colors ${
                showAllNumbers
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {showAllNumbers ? '隐藏所有数字' : '显示所有数字'}
            </button>
          </div>
        </div>
      );
    }

    const currentColor = activeColorTab as BasicColorType;
    const currentVisibility = colorVisibility[currentColor];
    const availableLevels = getAvailableLevels(currentColor);
    const colorClass = getColorCSSMap(currentColor);

    return (
      <div className="space-y-3">
        {/* 总控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {getColorName(currentColor)}格子总控制
          </label>
          <button
            onClick={handleColorVisibilityToggle}
            className={`w-full p-2 text-sm rounded border transition-colors ${
              currentVisibility.showCells
                ? 'bg-gray-700 text-white border-gray-600'
                : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
            }`}
          >
            {currentVisibility.showCells ? `隐藏所有${getColorName(currentColor)}格子` : `显示所有${getColorName(currentColor)}格子`}
          </button>
        </div>

        {/* 新增：数字显示控制（移植自传统面板） */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">数字显示控制</label>
          <button
            onClick={handleNumbersToggle}
            className={`w-full p-2 text-sm rounded border transition-colors ${
              showAllNumbers
                ? 'bg-blue-600 text-white border-blue-500'
                : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
            }`}
          >
            {showAllNumbers ? '隐藏所有数字' : '显示所有数字'}
          </button>
        </div>

        {/* 逐级控制 */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            {getColorName(currentColor)}逐级控制
          </label>
          <div className="grid grid-cols-4 gap-1">
            {[1, 2, 3, 4].map((level) => {
              const levelExists = availableLevels.includes(level as 1 | 2 | 3 | 4);
              
              if (!levelExists) {
                // Debug-3.3: 不存在的级别显示占位符
                return (
                  <div key={level} className="p-1.5 text-xs text-gray-500 border border-gray-700 rounded flex items-center justify-center opacity-30">
                    -
                  </div>
                );
              }
              
              const levelKey = `showLevel${level}` as keyof typeof currentVisibility;
              const isActive = currentVisibility[levelKey] === true; // 明确检查true值
              const levelClass = colorClass?.[`level${level}` as keyof typeof colorClass];
              const validLevelClass = levelClass || colorClass?.bg || 'bg-gray-500';

              // Debug: 记录按钮状态
              if (currentColor === 'red') {
                console.log(`🔘 红色级别${level}按钮状态:`, {
                  levelKey,
                  rawValue: currentVisibility[levelKey],
                  isActive,
                  currentVisibility
                });
              }
              
              return (
                <button
                  key={level}
                  onClick={() => handleLevelToggle(level as 1 | 2 | 3 | 4)}
                  className={`p-1.5 text-xs rounded border transition-colors flex items-center justify-center ${
                    isActive
                      ? `${validLevelClass} text-white border-transparent`
                      : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {getColorName(currentColor).charAt(0)}{level}
                </button>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  // 渲染数据信息
  const renderDataInfo = () => {
    if (activeColorTab === 'black') {
      return (
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">黑色格子数据</label>
                     <div className="text-xs text-gray-400 bg-gray-800 p-2 rounded">
             <div>显示状态: {blackCellData.visibility ? '开启' : '关闭'}</div>
             <div>坐标数量: {blackCellData.coordinates?.length || 0} 个</div>
           </div>
        </div>
      );
    }

    const currentColor = activeColorTab as BasicColorType;
    const coordinates = colorCoordinates[currentColor];
    const values = colorValues[currentColor];

    return (
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
                      {getColorName(currentColor)}数据信息
        </label>
        <div className="text-xs text-gray-400 bg-gray-800 p-2 rounded space-y-1">
          <div>Level 1: {coordinates?.level1?.length || 0} 个坐标</div>
          <div>Level 2: {coordinates?.level2?.length || 0} 个坐标</div>
          <div>Level 3: {coordinates?.level3?.length || 0} 个坐标</div>
          <div>Level 4: {coordinates?.level4?.length || 0} 个坐标</div>
          <div className="mt-2 pt-2 border-t border-gray-700">
            颜色值: {values?.hex || 'N/A'}
          </div>
        </div>
      </div>
    );
  };

  // Debug Phase-1.3: 渲染调试面板
  const renderDebugPanel = () => {
    if (!showDebugPanel) return null;

    const currentColor = activeColorTab as BasicColorType;
    const currentVisibility = activeColorTab !== 'black' ? colorVisibility[currentColor] : null;
    const availableLevels = activeColorTab !== 'black' ? getAvailableLevels(currentColor) : [];

    return (
      <div className="mt-4 p-3 bg-gray-800 border border-gray-600 rounded">
        <h4 className="text-sm font-medium text-yellow-300 mb-2">🐛 调试面板</h4>
        
        {activeColorTab !== 'black' && (
          <div className="space-y-2 text-xs text-gray-300">
            <div><strong>当前颜色:</strong> {getColorName(currentColor)}</div>
            <div><strong>可用级别:</strong> [{availableLevels.join(', ')}]</div>
            <div><strong>可见性状态:</strong></div>
            <div className="ml-2 space-y-1">
              <div>showCells: {currentVisibility?.showCells?.toString()}</div>
              {([1, 2, 3, 4] as const).map(level => {
                const levelKey = `showLevel${level}` as keyof typeof currentVisibility;
                const isAvailable = availableLevels.includes(level as 1 | 2 | 3 | 4);
                const isVisible = currentVisibility?.[levelKey] as boolean | undefined;
                return (
                  <div key={level} className={`${!isAvailable ? 'text-gray-500' : ''}`}>
                    showLevel{level}: {typeof isVisible === 'boolean' ? isVisible.toString() : 'undefined'} 
                    {!isAvailable && ' (不存在)'}
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        <div className="mt-3 space-y-2">
          <button
            onClick={handleRunConsistencyCheck}
            className="w-full p-1.5 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            🔍 运行数据一致性检查
          </button>
          
          {activeColorTab !== 'black' && (
            <button
              onClick={handleCheckCurrentColor}
              className="w-full p-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              🎨 检查当前颜色: {getColorName(currentColor)}
            </button>
          )}
          
          <div className="text-xs text-gray-400 mt-2">
            💡 打开浏览器控制台查看详细结果
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 颜色选项卡 */}
      {renderColorTabs()}

      {/* 级别控制 */}
      {renderLevelControls()}

      {/* 数据信息 */}
      {renderDataInfo()}

      {/* Debug Phase-1.3: 调试控制 */}
      <div className="pt-2 border-t border-gray-700">
        <button
          onClick={handleDebugToggle}
          className={`w-full p-2 text-xs rounded border transition-colors ${
            showDebugPanel
              ? 'bg-yellow-600 text-white border-yellow-500'
              : 'bg-transparent text-gray-400 border-gray-600 hover:bg-gray-700'
          }`}
        >
          🐛 {showDebugPanel ? '隐藏' : '显示'}调试面板
        </button>
        
        {renderDebugPanel()}
      </div>
    </div>
  );
});

BasicDataPanel.displayName = 'BasicDataPanel'; 