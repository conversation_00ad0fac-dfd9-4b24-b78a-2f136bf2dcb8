import React, { memo, useCallback } from 'react';
import { ColorGroupSelectorProps } from './types';

/**
 * 颜色分组选择器组件
 * 🎯 核心价值：处理颜色分组选择逻辑，支持标准分组(1-10)和网格分组(11-44)
 * ⚡ 性能优化：基于R1.1成功模式，使用memo和useCallback
 */
export const ColorGroupSelector = memo<ColorGroupSelectorProps>((props) => {
  const {
    colorType,
    selectedGroups,
    onGroupToggle,
    onShowAllGroups,
    onHideAllGroups
  } = props;

  // 基于R1.1成功模式：useCallback稳定化事件处理
  const handleGroupToggle = useCallback((group: number) => {
    onGroupToggle(group);
  }, [onGroupToggle]);

  const handleShowAllGroups = useCallback(() => {
    onShowAllGroups();
  }, [onShowAllGroups]);

  const handleHideAllGroups = useCallback(() => {
    onHideAllGroups();
  }, [onHideAllGroups]);

  // 根据颜色类型确定分组范围
  // 红、青、黄、紫使用1-10标准分组
  // 橙、绿、蓝、粉使用11-44网格分组
  const getGroupsForColor = (colorType: string) => {
    if (['red', 'cyan', 'yellow', 'purple'].includes(colorType)) {
      return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 标准分组
    } else {
      return [11, 12, 13, 14, 21, 22, 23, 24, 31, 32, 33, 34, 41, 42, 43, 44]; // 网格分组
    }
  };

  const availableGroups = getGroupsForColor(colorType);
  const isStandardGroups = availableGroups.every(g => g <= 10);

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-600 mb-2">
        {isStandardGroups ? '标准分组控制 (1-10)' : '网格分组控制 (11-44)'}
      </label>
      
      {/* 分组按钮网格 */}
      <div className={`grid gap-1 ${isStandardGroups ? 'grid-cols-5' : 'grid-cols-4'}`}>
        {availableGroups.map((group: number) => (
          <button
            key={group}
            onClick={() => handleGroupToggle(group)}
            className={`p-1 text-xs rounded border transition-colors ${
              selectedGroups.has(group)
                ? 'bg-gray-200 text-gray-800 border-gray-400'
                : 'bg-transparent text-gray-700 border-gray-200 hover:bg-gray-100'
            }`}
          >
            {isStandardGroups 
              ? ['➊', '➋', '➌', '➍', '➎', '➏', '➐', '➑', '➒', '➓'][group - 1]
              : group.toString()
            }
          </button>
        ))}
      </div>

      {/* 全选/全不选按钮 */}
      <div className="flex gap-2">
        <button
          onClick={() => availableGroups.forEach((g: number) => onGroupToggle(g))}
          className="flex-1 p-1 text-xs bg-transparent text-gray-700 rounded border hover:bg-gray-100 transition-colors"
        >
          {selectedGroups.size === availableGroups.length ? '隐藏所有分组' : '显示所有分组'}
        </button>
      </div>
    </div>
  );
});

ColorGroupSelector.displayName = 'ColorGroupSelector';

// 🔧 辅助函数：获取颜色对应的CSS类前缀
function getColorForGroup(colorType: string): string {
  // 映射到Tailwind CSS颜色类
  switch (colorType) {
    case 'red': return 'red';
    case 'orange': return 'orange';
    case 'yellow': return 'yellow';
    case 'green': return 'green';
    case 'cyan': return 'cyan';
    case 'blue': return 'blue';
    case 'purple': return 'purple';
    case 'pink': return 'pink';
    default: return 'gray';
  }
} 