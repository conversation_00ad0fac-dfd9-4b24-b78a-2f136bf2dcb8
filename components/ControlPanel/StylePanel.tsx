import React, { memo, useCallback } from 'react';
import { useDynamicStyleStore } from '../../stores/dynamicStyleStore';
import { useStyleStore } from '../../stores/styleStore';

/**
 * 样式面板组件
 * 🎯 核心价值：统一管理基础样式常量、动态样式配置
 * ⚡ 性能优化：使用memo + useCallback模式
 * 📊 功能范围：字体、边距、形状、显示模式、圆形缩放、主题等
 */

interface StylePanelProps {
  className?: string;
}

export const StylePanel = memo<StylePanelProps>(({ className = '' }) => {
  // 从新Store获取状态和操作
  const {
    fontSize,
    matrixMargin, 
    cellShape,
    displayMode,
    enableCircleScale,
    circleScaleFactor,
    enableVirtualization,
    setFontSize,
    setMatrixMargin,
    setCellShape,
    setDisplayMode,
    toggleCircleScale,
    setCircleScaleFactor,
    toggleVirtualization,
  } = useDynamicStyleStore();

  const {
    currentTheme,
    setCurrentTheme,
  } = useStyleStore();

  // 事件处理函数
  const handleFontSizeChange = useCallback((value: number) => {
    setFontSize(value);
  }, [setFontSize]);

  const handleMatrixMarginChange = useCallback((value: number) => {
    setMatrixMargin(value);
  }, [setMatrixMargin]);

  const handleCellShapeChange = useCallback((shape: 'square' | 'rounded' | 'circle') => {
    setCellShape(shape);
  }, [setCellShape]);

  const handleDisplayModeChange = useCallback((mode: 'number' | 'coordinate' | 'hidden') => {
    setDisplayMode(mode);
  }, [setDisplayMode]);

  const handleCircleScaleToggle = useCallback(() => {
    toggleCircleScale();
  }, [toggleCircleScale]);

  const handleCircleScaleFactorChange = useCallback((value: number) => {
    setCircleScaleFactor(value);
  }, [setCircleScaleFactor]);

  const handleThemeChange = useCallback((theme: 'light' | 'dark') => {
    setCurrentTheme(theme);
  }, [setCurrentTheme]);

  const handleVirtualizationToggle = useCallback(() => {
    toggleVirtualization();
  }, [toggleVirtualization]);

  const availableThemes: ('light' | 'dark')[] = ['light', 'dark'];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 主题控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">界面主题</label>
        <div className="grid grid-cols-2 gap-2">
          {availableThemes.map((theme) => (
            <button
              key={theme}
              onClick={() => handleThemeChange(theme)}
              className={`p-2 text-sm rounded border transition-colors ${
                currentTheme === theme
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {theme === 'dark' ? '深色主题' : '浅色主题'}
            </button>
          ))}
        </div>
      </div>

      {/* 字体大小控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          字体大小: {fontSize}px
        </label>
        <input
          type="range"
          min="8"
          max="24"
          step="1"
          value={fontSize}
          onChange={(e) => handleFontSizeChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>8px</span>
          <span>24px</span>
        </div>
      </div>

      {/* 矩阵边距控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          矩阵边距: {matrixMargin}px
        </label>
        <input
          type="range"
          min="5"
          max="50"
          step="1"
          value={matrixMargin}
          onChange={(e) => handleMatrixMarginChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>5px</span>
          <span>50px</span>
        </div>
      </div>

      {/* 单元格形状控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">单元格形状</label>
        <div className="grid grid-cols-3 gap-2">
          {[
            { value: 'square', label: '方形' },
            { value: 'rounded', label: '圆角' },
            { value: 'circle', label: '圆形' }
          ].map(({ value, label }) => (
            <button
              key={value}
              onClick={() => handleCellShapeChange(value as any)}
              className={`p-2 text-sm rounded border transition-colors ${
                cellShape === value
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* 显示模式控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">显示模式</label>
        <div className="grid grid-cols-3 gap-2">
          {[
            { value: 'number', label: '数字' },
            { value: 'coordinate', label: '坐标' },
            { value: 'hidden', label: '隐藏' }
          ].map(({ value, label }) => (
            <button
              key={value}
              onClick={() => handleDisplayModeChange(value as any)}
              className={`p-2 text-sm rounded border transition-colors ${
                displayMode === value
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* 圆形缩放控制 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="text-sm font-medium text-gray-300">圆形缩放</label>
          <button
            onClick={handleCircleScaleToggle}
            className={`px-3 py-1 text-xs rounded border transition-colors ${
              enableCircleScale
                ? 'bg-green-600 text-white border-green-500'
                : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
            }`}
          >
            {enableCircleScale ? '开启' : '关闭'}
          </button>
        </div>
        
        {enableCircleScale && (
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              缩放系数: {circleScaleFactor.toFixed(1)}
            </label>
            <input
              type="range"
              min="1.0"
              max="2.0"
              step="0.1"
              value={circleScaleFactor}
              onChange={(e) => handleCircleScaleFactorChange(Number(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>1.0x</span>
              <span>2.0x</span>
            </div>
          </div>
        )}
      </div>

      {/* 🚨 格子变形bug修复：虚拟滚动控制 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <div className="flex flex-col">
            <label className="text-sm font-medium text-gray-300">虚拟滚动</label>
            <span className="text-xs text-gray-400">大网格性能优化</span>
          </div>
          <button
            onClick={handleVirtualizationToggle}
            className={`px-3 py-1 text-xs rounded border transition-colors ${
              enableVirtualization
                ? 'bg-orange-600 text-white border-orange-500'
                : 'bg-green-600 text-white border-green-500'
            }`}
          >
            {enableVirtualization ? '关闭' : '开启'}
          </button>
        </div>
        
        <div className="p-2 bg-gray-800 border border-gray-600 rounded text-xs text-gray-300">
          {enableVirtualization ? (
            <span className="text-orange-300">⚠️ 虚拟滚动可能导致格子变形，如遇问题请关闭</span>
          ) : (
            <span className="text-green-300">✅ 已禁用虚拟滚动，格子形状稳定</span>
          )}
        </div>
      </div>
    </div>
  );
});

StylePanel.displayName = 'StylePanel'; 