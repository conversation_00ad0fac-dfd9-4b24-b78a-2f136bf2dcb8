import React, { memo, useCallback } from 'react';
import { ColorLevelToggleProps } from './types';

/**
 * 颜色级别切换组件
 * 🎯 统一管理所有颜色的级别显示控制（1级、2级、3级、4级）
 * ⚡ 性能优化：使用memo和useCallback保持性能
 */
export const ColorLevelToggle = memo<ColorLevelToggleProps>((props) => {
  const {
    colorType,
    showLevels,
    onLevelToggle
  } = props;

  const handleLevelToggle = useCallback((level: 1 | 2 | 3 | 4) => {
    onLevelToggle(level);
  }, [onLevelToggle]);

  // 获取级别按钮样式
  const getLevelButtonStyle = (level: 1 | 2 | 3 | 4) => {
    const isActive = showLevels[level];
    const baseStyle = "flex-1 px-2 py-1 text-xs rounded transition-colors";
    
    if (isActive) {
      return `${baseStyle} bg-${getColorForLevel(colorType)}-500 text-white`;
    } else {
      return `${baseStyle} bg-gray-100 text-gray-600 hover:bg-gray-200`;
    }
  };

  // 获取级别标签
  const getLevelLabel = (level: 1 | 2 | 3 | 4): string => {
    return `${level}级`;
  };

  // 检查级别是否可用
  const isLevelAvailable = (level: 1 | 2 | 3 | 4): boolean => {
    // 某些颜色没有2级（如orange, green, blue, pink）
    if (level === 2) {
      return ['red', 'cyan', 'yellow', 'purple'].includes(colorType);
    }
    return true;
  };

  return (
    <div>
      <label className="block text-sm font-medium text-gray-600 mb-2">
        级别控制
      </label>
      <div className="grid grid-cols-4 gap-1">
        {([1, 2, 3, 4] as const).map(level => (
          <button
            key={level}
            onClick={() => handleLevelToggle(level)}
            disabled={!isLevelAvailable(level)}
            className={`${getLevelButtonStyle(level)} ${
              !isLevelAvailable(level) ? 'opacity-30 cursor-not-allowed' : ''
            }`}
          >
            {getLevelLabel(level)}
          </button>
        ))}
      </div>
    </div>
  );
});

ColorLevelToggle.displayName = 'ColorLevelToggle';

// 🔧 辅助函数：获取颜色对应的CSS类前缀
function getColorForLevel(colorType: string): string {
  // 映射到Tailwind CSS颜色类
  switch (colorType) {
    case 'red': return 'red';
    case 'orange': return 'orange';
    case 'yellow': return 'yellow';
    case 'green': return 'green';
    case 'cyan': return 'cyan';
    case 'blue': return 'blue';
    case 'purple': return 'purple';
    case 'pink': return 'pink';
    default: return 'gray';
  }
} 