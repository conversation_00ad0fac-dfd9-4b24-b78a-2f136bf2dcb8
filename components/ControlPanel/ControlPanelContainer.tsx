import React, { memo } from 'react';
import { ControlPanelContainerProps } from './types';
import { StylePanel } from './StylePanel';
import { BasicDataPanel } from './BasicDataPanel';
import { CombinationBusinessPanel } from './CombinationBusinessPanel';

/**
 * 控制面板容器组件 - R2升级版
 * 🎯 核心价值：支持新的3个逻辑面板架构，同时兼容原有功能
 * ⚡ 性能优化：基于R1.1成功模式，使用memo包装
 * 🔧 新架构：样式、基础数据、组合业务三个主面板
 * 🔄 兼容性：保持原有颜色面板功能，渐进式迁移
 */
export const ControlPanelContainer = memo<ControlPanelContainerProps>((props) => {
  const { 
    activePanel, 
    setActivePanel, 
    getTabStyle,
    versionProps
  } = props;

  // 确定当前模式：新架构 vs 传统模式
  const isNewArchitecture = ['r2-style', 'r2-data', 'r2-combination'].includes(activePanel);

  return (
    <div className="w-72 bg-white flex flex-col overflow-hidden border-l border-gray-300 pl-2">
      {/* 现代卡片式菜单栏 - R2升级版 */}
      <div className="space-y-1 px-1 py-2">
        {/* R2新架构主面板 */}
        <div className="border-b border-gray-200 pb-2">
          <div className="text-xs font-medium text-gray-500 mb-1 px-1">R2新架构</div>
          <div className="grid grid-cols-3 gap-1">
            {[
              { key: 'r2-style', label: '样式' },
              { key: 'r2-data', label: '数据' },
              { key: 'r2-combination', label: '组合' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActivePanel(tab.key as any)}
                className={`px-2 py-2 text-xs font-medium rounded-md transition-all duration-300 ${
                  activePanel === tab.key
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                }`}
              >
                <div className="text-center">
                  <div className="text-xs">{tab.label}</div>
                </div>
              </button>
            ))}
          </div>
        </div>


      </div>

      {/* 控制面板内容 - 支持新旧架构 */}
      <div className="flex-1 p-3 overflow-y-auto scrollbar-thin bg-white">
        {/* R2新架构面板 */}
        {activePanel === 'r2-style' && (
          <div>
            <div className="mb-3 p-2 bg-blue-50 rounded border border-blue-200">
              <div className="text-sm font-medium text-blue-800">🎨 样式面板</div>
              <div className="text-xs text-blue-600">统一管理基础样式和动态样式配置</div>
            </div>
            <StylePanel />
          </div>
        )}

        {activePanel === 'r2-data' && (
          <div>
            <div className="mb-3 p-2 bg-green-50 rounded border border-green-200">
              <div className="text-sm font-medium text-green-800">📊 基础数据面板</div>
              <div className="text-xs text-green-600">统一管理8种颜色数据和黑色格子</div>
            </div>
            <BasicDataPanel />
          </div>
        )}

        {activePanel === 'r2-combination' && (
          <div>
            <div className="mb-3 p-2 bg-purple-50 rounded border border-purple-200">
              <div className="text-sm font-medium text-purple-800">🔗 组合业务面板</div>
              <div className="text-xs text-purple-600">管理组合模式、业务逻辑和版本</div>
            </div>
            <CombinationBusinessPanel />
          </div>
        )}

        {/* 传统面板功能已全部移植到R2新面板，显示迁移提示 */}
        {!isNewArchitecture && (
          <div className="p-4 bg-blue-50 rounded border border-blue-200">
            <div className="text-sm font-medium text-blue-800 mb-2">✨ 功能迁移提示</div>
            <div className="text-xs text-blue-600 space-y-1">
              <div>• 传统面板功能已全部移植到R2新架构</div>
              <div>• 请使用【样式】【数据】【组合】面板</div>
              <div>• 体验更加统一高效的操作界面</div>
            </div>
            <button
              onClick={() => setActivePanel('r2-style')}
              className="mt-3 w-full p-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              切换到R2样式面板
            </button>
          </div>
        )}
      </div>
    </div>
  );
});

ControlPanelContainer.displayName = 'ControlPanelContainer'; 