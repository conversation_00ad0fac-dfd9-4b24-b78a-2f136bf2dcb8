// 控制面板组件统一导出 - R2新架构
export { ControlPanelContainer } from './ControlPanelContainer';

// R2新架构主面板组件
export { StylePanel } from './StylePanel';
export { BasicDataPanel } from './BasicDataPanel';
export { CombinationBusinessPanel } from './CombinationBusinessPanel';

// 共享组件
export { VersionPanel } from './VersionPanel';

// 传统面板组件（兼容保留，建议迁移到R2）
export { ColorSystemPanel } from './ColorSystemPanel';
export { ColorLevelToggle } from './ColorLevelToggle';
export { ColorGroupSelector } from './ColorGroupSelector';

// 导出类型
export type {
  ControlPanelContainerProps,
  ColorSystemPanelProps,
  VersionPanelProps,
  ColorLevelToggleProps,
  ColorGroupSelectorProps,
  ColorSystemType
} from './types'; 