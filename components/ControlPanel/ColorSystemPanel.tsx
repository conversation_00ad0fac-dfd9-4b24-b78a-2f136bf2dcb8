import React, { memo, useCallback } from 'react';
import { ColorSystemPanelProps, ColorSystemType } from './types';
import { useStyleStore } from '../../stores/styleStore';

/**
 * 颜色系统面板组件
 * 🎯 核心价值：统一8种颜色面板的重复逻辑，根据currentColorType动态显示
 * ⚡ 性能优化：基于R1.1成功模式，使用memo + useCallback
 * 🔧 逻辑还原：完全匹配原本每个颜色面板的4个部分实现
 */

// 根据颜色类型获取可用分组
const getAvailableGroups = (colorType: ColorSystemType): number[] => {
  if (['red', 'cyan', 'yellow', 'purple'].includes(colorType)) {
    return Array.from({ length: 10 }, (_, i) => i + 1); // 1-10
  }
  if (['orange', 'green', 'blue', 'pink'].includes(colorType)) {
    return [11, 12, 13, 14, 21, 22, 23, 24, 31, 32, 33, 34, 41, 42, 43, 44]; // 网格分组
  }
  return [];
};

// 根据组号和颜色类型获取显示名称
const getGroupDisplayName = (group: number, colorType: ColorSystemType): string => {
  if (['red', 'cyan', 'yellow', 'purple'].includes(colorType)) {
    return ['➊', '➋', '➌', '➍', '➎', '➏', '➐', '➑', '➒', '➓'][group - 1] || String(group);
  }
  if (['orange', 'green', 'blue', 'pink'].includes(colorType)) {
    const v = Math.floor(group / 10);
    const h = group % 10;
    return `竖${v}横${h}`;
  }
  return String(group);
};

// 颜色映射表已迁移到constants/ui/css-mappings.ts

export const ColorSystemPanel = memo<ColorSystemPanelProps>((props) => {
  const {
    colorType,
    showLevels, onLevelToggle,
    selectedGroups, onGroupToggle,
    swapGroup1, onSwapGroup1Change,
    swapGroup2, onSwapGroup2Change,
    onSwapGroups,
    showCells, onShowCellsToggle,
  } = props;

  // 从 styleStore 获取 CSS 映射
  const { getColorCSSMap } = useStyleStore();

  const handleLevelToggle = useCallback((level: 1 | 2 | 3 | 4) => onLevelToggle(level), [onLevelToggle]);
  const handleGroupToggle = useCallback((group: number) => onGroupToggle(group), [onGroupToggle]);
  const handleCellsToggle = useCallback(() => onShowCellsToggle(), [onShowCellsToggle]);
  
  const availableLevels = ['orange', 'green', 'blue', 'pink'].includes(colorType) ? [1, 3, 4] : [1, 2, 3, 4];
  const availableGroups = getAvailableGroups(colorType);
  
  const handleToggleAllGroups = useCallback(() => {
    const allSelected = selectedGroups.size === availableGroups.length;
    if (allSelected) {
      availableGroups.forEach(group => onGroupToggle(group));
    } else {
      availableGroups.forEach(group => {
        if (!selectedGroups.has(group)) {
          onGroupToggle(group);
        }
      });
    }
  }, [selectedGroups, availableGroups, onGroupToggle]);

  const colorNames = { red: '红色', orange: '橙色', cyan: '青色', yellow: '黄色', purple: '紫色', green: '绿色', blue: '蓝色', pink: '粉色', black: '黑色' };
  const colorName = colorNames[colorType] || '未知颜色';
  
  // 黑色面板特殊处理
  if (colorType === 'black') {
    return (
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">黑色格子显示控制</label>
          <button 
            onClick={handleCellsToggle} 
            className={`w-full p-2 text-sm rounded border transition-colors ${
              showCells ? 'bg-black text-white border-gray-600' : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
            }`}
          >
            {showCells ? '隐藏黑色格子' : '显示黑色格子'}
          </button>
        </div>
      </div>
    );
  }
  
  const colorClasses = getColorCSSMap(colorType) || {};

  return (
    <div className="space-y-3">
      {/* 总控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">{colorName}格子总控制</label>
        <button onClick={handleCellsToggle} className={`w-full p-2 text-sm rounded border transition-colors ${showCells ? 'bg-gray-700 text-white border-gray-600' : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'}`}>
          {showCells ? `隐藏所有${colorName}格子` : `显示所有${colorName}格子`}
        </button>
      </div>

      {/* 逐级控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">{colorName}逐级控制</label>
        <div className="grid grid-cols-4 gap-1">
          {availableLevels.map((level) => {
            const levelKey = level as 1 | 2 | 3 | 4;
            // 安全检查：确保 showLevels 存在且具有正确的结构
            const isActive = showLevels?.[levelKey] ?? false;
            const levelClass = colorClasses[`level${levelKey}` as keyof typeof colorClasses];
            
            // 确保级别样式正确应用，如果找不到对应级别则使用level1作为备选
            const validLevelClass = levelClass || colorClasses.level1 || colorClasses.bg || 'bg-gray-500';
            
            return (
              <button 
                key={level} 
                onClick={() => handleLevelToggle(levelKey)} 
                className={`p-1.5 text-xs rounded border transition-colors flex items-center justify-center ${
                  isActive 
                    ? `${validLevelClass} text-white border-transparent` 
                    : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'
                }`}
              >
                {colorName.charAt(0)}{level}
              </button>
            );
          })}
        </div>
      </div>

      {/* 分组控制 */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">{colorName}分组控制</label>
        <div className={`grid gap-1 ${['red', 'cyan', 'yellow', 'purple'].includes(colorType) ? 'grid-cols-5' : 'grid-cols-4'}`}>
          {availableGroups.map((group) => (
            <button key={group} onClick={() => handleGroupToggle(group)} className={`p-1 text-xs rounded-md border transition-colors flex items-center justify-center ${selectedGroups.has(group) ? 'bg-gray-700 text-white border-gray-600' : 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700'}`}>
              {getGroupDisplayName(group, colorType)}
            </button>
          ))}
        </div>
        <button onClick={handleToggleAllGroups} className="w-full mt-2 p-1 text-xs bg-transparent text-gray-300 rounded-md border border-gray-600 hover:bg-gray-700 transition-colors">
          {selectedGroups.size === availableGroups.length ? '隐藏所有分组' : '显示所有分组'}
        </button>
      </div>

      {/* 组别互换 */}
      <div>
        <label className="block text-sm font-medium text-gray-600 mb-2">{colorName}组别互换</label>
        <div className="flex gap-2 items-center mb-2">
          <select value={swapGroup1} onChange={(e) => onSwapGroup1Change(Number(e.target.value))} className="flex-1 p-1 text-xs border rounded-md bg-white">
            {availableGroups.map((num) => (<option key={num} value={num}>组 {getGroupDisplayName(num, colorType)}</option>))}
          </select>
          <span className="text-xs text-gray-500">↔</span>
          <select value={swapGroup2} onChange={(e) => onSwapGroup2Change(Number(e.target.value))} className="flex-1 p-1 text-xs border rounded-md bg-white">
            {availableGroups.map((num) => (<option key={num} value={num}>组 {getGroupDisplayName(num, colorType)}</option>))}
          </select>
        </div>
        <button onClick={onSwapGroups} className={`w-full p-2 text-xs bg-transparent rounded-md border transition-colors ${colorClasses.text} ${colorClasses.border} ${colorClasses.hoverBg}`}>
          执行{colorName}组别互换
        </button>
      </div>
    </div>
  );
});

ColorSystemPanel.displayName = 'ColorSystemPanel'; 