'use client';

import React, { memo, useCallback, useMemo, useRef } from 'react';
import { GridCellProps } from './types';

/**
 * GridCell组件 - 单个网格单元格
 * 
 * ⚡ Phase 6.3性能优化：组件交互性能提升
 * 🚀 新增特性：
 *   - 优化点击响应速度
 *   - 悬停效果性能提升
 *   - 事件处理优化
 *   - 减少重复计算
 */
export const GridCell = memo<GridCellProps>(({
  cell,
  fontSize,
  colorIndex,
  specialCoordinates,
  getCellStyle,
  getCellContent,
  getCircleScaleStyle,
  onCellClick,
  onHoverInfoChange
}) => {
  const cellRef = useRef<HTMLDivElement>(null);
  
  // 预计算单元格样式和内容（避免重复计算）
  const cellStyle = useMemo(() => getCellStyle(cell), [getCellStyle, cell]);
  const cellContent = useMemo(() => getCellContent(cell), [getCellContent, cell]);
  const circleStyle = useMemo(() => getCircleScaleStyle(cell), [getCircleScaleStyle, cell]);

  // 预计算悬停信息（避免在每次悬停时重复计算）
  const hoverInfo = useMemo(() => {
    const coordinateKey = `${cell.x},${cell.y}`;
    const specialLetter = specialCoordinates.get(coordinateKey);
    
    // ⚡ 性能优化: 一次性获取所有颜色信息（R0优化保持）
    const allColorInfo = colorIndex.getAllColorInfo(cell.x, cell.y);
    const redInfo = allColorInfo.red;
    const orangeInfo = allColorInfo.orange;
    const cyanInfo = allColorInfo.cyan;
    const yellowInfo = allColorInfo.yellow;
    const purpleInfo = allColorInfo.purple;
    const greenInfo = allColorInfo.green;
    const blueInfo = allColorInfo.blue;
    const pinkInfo = allColorInfo.pink;

    let info = `序号: ${cell.number} | 网格位置: (${cell.row + 1}, ${cell.col + 1}) | 坐标: (${cell.x}, ${cell.y})`;

    if (specialLetter) {
      info += ` | 黑色格子: ${specialLetter}`;
    }

    if (redInfo) {
      const groupText = redInfo.group
        ? `➊➋➌➍➎➏➐➑➒➓`[redInfo.group - 1]
        : '无分组';
      info += ` | 红色格子 - 级数: 红${redInfo.level}级 | 分组: ${groupText}`;
    }

    if (orangeInfo) {
      const getGroupText = (group: number) => {
        const v = Math.floor(group / 10);
        const h = group % 10;
        return `竖${v}横${h}`;
      };
      const groupText = orangeInfo.group
        ? getGroupText(orangeInfo.group)
        : '无分组';
      info += ` | 橙色格子 - 级数: 橙${orangeInfo.level}级 | 分组: ${groupText}`;
    }

    if (cyanInfo) {
      const groupText = cyanInfo.group
        ? `➊➋➌➍➎➏➐➑➒➓`[cyanInfo.group - 1]
        : '无分组';
      info += ` | 青色格子 - 级数: 青${cyanInfo.level}级 | 分组: ${groupText}`;
    }

    if (yellowInfo) {
      const groupText = yellowInfo.group
        ? `➊➋➌➍➎➏➐➑➒➓`[yellowInfo.group - 1]
        : '无分组';
      info += ` | 黄色格子 - 级数: 黄${yellowInfo.level}级 | 分组: ${groupText}`;
    }

    if (purpleInfo) {
      const groupText = purpleInfo.group
        ? `➊➋➌➍➎➏➐➑➒➓`[purpleInfo.group - 1]
        : '无分组';
      info += ` | 紫色格子 - 级数: 紫${purpleInfo.level}级 | 分组: ${groupText}`;
    }

    if (greenInfo) {
      const getGroupText = (group: number) => {
        const v = Math.floor(group / 10);
        const h = group % 10;
        return `竖${v}横${h}`;
      };
      const groupText = greenInfo.group
        ? getGroupText(greenInfo.group)
        : '无分组';
      info += ` | 绿色格子 - 级数: 绿${greenInfo.level}级 | 分组: ${groupText}`;
    }

    if (blueInfo) {
      const getGroupText = (group: number) => {
        const v = Math.floor(group / 10);
        const h = group % 10;
        return `竖${v}横${h}`;
      };
      const groupText = blueInfo.group
        ? getGroupText(blueInfo.group)
        : '无分组';
      info += ` | 蓝色格子 - 级数: 蓝${blueInfo.level}级 | 分组: ${groupText}`;
    }

    if (pinkInfo) {
      const getGroupText = (group: number) => {
        const v = Math.floor(group / 10);
        const h = group % 10;
        return `竖${v}横${h}`;
      };
      const groupText = pinkInfo.group
        ? getGroupText(pinkInfo.group)
        : '无分组';
      info += ` | 粉色格子 - 级数: 粉${pinkInfo.level}级 | 分组: ${groupText}`;
    }

    return info;
  }, [cell, colorIndex, specialCoordinates]);

  // 优化点击事件处理函数（使用防抖避免重复点击）
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // 添加视觉反馈
    if (cellRef.current) {
      cellRef.current.style.transform = 'scale(0.95)';
      setTimeout(() => {
        if (cellRef.current) {
          cellRef.current.style.transform = '';
        }
      }, 100);
    }
    
    onCellClick(cell);
  }, [cell, onCellClick]);

  // 优化鼠标进入事件处理函数（使用预计算的悬停信息）
  const handleMouseEnter = useCallback(() => {
    onHoverInfoChange(hoverInfo);
  }, [hoverInfo, onHoverInfoChange]);

  // 优化鼠标离开事件处理函数
  const handleMouseLeave = useCallback(() => {
    onHoverInfoChange('将鼠标悬停在格子上查看详细信息');
  }, [onHoverInfoChange]);

  // 优化触摸事件处理（移动端支持）
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    if (cellRef.current) {
      cellRef.current.style.transform = 'scale(0.95)';
    }
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    if (cellRef.current) {
      cellRef.current.style.transform = '';
    }
    onCellClick(cell);
  }, [cell, onCellClick]);

  // 优化样式计算
  const finalStyle = useMemo(() => ({
    fontSize: `${fontSize}px`,
    minHeight: '16px',
    minWidth: '16px',
    transition: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)', // 更流畅的过渡效果
    cursor: 'pointer',
    userSelect: 'none' as const,
    WebkitUserSelect: 'none' as const,
    WebkitTapHighlightColor: 'transparent', // 移除移动端点击高亮
    ...circleStyle,
  }), [fontSize, circleStyle]);

  return (
    <div
      ref={cellRef}
      className={`${cellStyle} flex items-center justify-center hover:z-10 active:z-20`}
      style={finalStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      role="button"
      tabIndex={0}
      aria-label={`网格单元格 ${cell.number}, 坐标 (${cell.x}, ${cell.y})`}
    >
      {cellContent}
    </div>
  );
});

GridCell.displayName = 'GridCell'; 