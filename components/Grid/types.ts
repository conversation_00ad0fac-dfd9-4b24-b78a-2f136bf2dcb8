import { CellData } from '@/types/grid';
import { ColorCoordinateIndex } from '@/utils/colorSystem';

export interface GridContainerProps {
  gridData: CellData[];
  onCellClick: (cell: CellData) => void;
  fontSize: number;
  matrixMargin: number;
  cellShape: 'square' | 'rounded' | 'circle';
  displayMode: 'visible' | 'hidden' | 'number' | 'coordinate';
  hoverInfo: string;
  setHoverInfo: (info: string) => void;
  // R0性能优化相关依赖
  colorIndex: ColorCoordinateIndex;
  specialCoordinates: Map<string, string>;
  // 从主文件传入的关键函数（保持R0性能优化）
  getCellStyle: (cell: CellData) => string;
  getCellContent: (cell: CellData) => React.ReactNode;
  getCircleScaleStyle: (cell: CellData) => React.CSSProperties;
}

export interface GridCellProps {
  cell: CellData;
  fontSize: number;
  // R0性能优化相关依赖
  colorIndex: ColorCoordinateIndex;
  specialCoordinates: Map<string, string>;
  // 从主文件传入的关键函数（保持R0性能优化）
  getCellStyle: (cell: CellData) => string;
  getCellContent: (cell: CellData) => React.ReactNode;
  getCircleScaleStyle: (cell: CellData) => React.CSSProperties;
  // 事件处理函数
  onCellClick: (cell: CellData) => void;
  onHoverInfoChange: (info: string) => void;
}

export interface GridOverlayProps {
  hoverInfo: string;
}

export interface GridConfig {
  rows: number;
  cols: number;
  cellSize: number;
  gap: number;
  showNumbers: boolean;
  showCoordinates: boolean;
}

export interface GridInteractionState {
  selectedCells: Set<number>;
  hoveredCell: CellData | null;
  dragSelection: boolean;
  multiSelect: boolean;
} 