'use client';

import React, { memo } from 'react';
import { GridOverlayProps } from './types';

/**
 * GridOverlay组件 - 网格悬停信息显示层
 * 
 * 提取悬停信息显示逻辑，简化主文件复杂度
 */
export const GridOverlay = memo<GridOverlayProps>(({
  hoverInfo
}) => {
  return (
    <div className="space-y-3 p-3 border-b border-gray-100">
      <h3 className="text-sm font-medium text-gray-700 mb-2">
        格子信息
      </h3>
      <div className="p-2 bg-gray-50 border border-gray-200 rounded text-xs text-gray-600 leading-tight font-mono break-words min-h-[50px]">
        {hoverInfo || '将鼠标悬停在网格上以查看信息'}
      </div>
    </div>
  );
});

GridOverlay.displayName = 'GridOverlay'; 