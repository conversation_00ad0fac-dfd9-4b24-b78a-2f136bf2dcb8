export interface CellData {
  id: number;
  number: number;
  color: string;
  level: number;
  group: null | number;
  row: number;
  col: number;
  x: number; // x坐标
  y: number; // y坐标
}

export interface GridDimensions {
  rows: number;
  cols: number;
  cellSize: number;
  gap: number;
}

export interface GridConfig {
  fontSize: number;
  matrixMargin: number;
  gridColor: string;
  cellShape: 'rounded' | 'circle' | 'square';
  displayMode: 'hidden' | 'visible';
} 