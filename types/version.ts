import { ColorCoordinates } from './color';

export interface VersionBase {
  timestamp: string;
  name?: string;
  metadata?: Record<string, unknown>;
}

// 默认版本系统类型
export interface DefaultVersion extends VersionBase {
  redCoords: ColorCoordinates;
  cyanCoords: ColorCoordinates;
  yellowCoords: ColorCoordinates;
  purpleCoords: ColorCoordinates;
  orangeCoords: ColorCoordinates;
  greenCoords: ColorCoordinates;
  blueCoords: ColorCoordinates;
  pinkCoords: ColorCoordinates;
}

// 分组模式版本类型
export interface GroupModeVersion extends VersionBase {
  fontSize: number;
  cellShape: 'rounded' | 'circle' | 'square';
  displayMode: 'hidden' | 'visible';
  enableCircleScale: boolean;
  circleScaleFactor: number;
  activePanel: string;
  settingsSubPage: 'main' | 'groupMode' | 'mixedMode';
  singleColorMode: string | null;
  showAllColors: boolean;
  showAllNumbers: boolean;
  showAllLevel1: boolean;
  showAllLevel2: boolean;
  showAllLevel3: boolean;
  showAllLevel4: boolean;
  showSpecificGroup: number | null;
  // 各种颜色的显示状态
  showBlackCells: boolean;
  showRedCells: boolean;
  showRedLevel1: boolean;
  showRedLevel2: boolean;
  showRedLevel3: boolean;
  showRedLevel4: boolean;
  showCyanCells: boolean;
  showCyanLevel1: boolean;
  showCyanLevel2: boolean;
  showCyanLevel3: boolean;
  showCyanLevel4: boolean;
  showYellowCells: boolean;
  showYellowLevel1: boolean;
  showYellowLevel2: boolean;
  showYellowLevel3: boolean;
  showYellowLevel4: boolean;
  showPurpleCells: boolean;
  showPurpleLevel1: boolean;
  showPurpleLevel2: boolean;
  showPurpleLevel3: boolean;
  showPurpleLevel4: boolean;
  showOrangeCells: boolean;
  showOrangeLevel1: boolean;
  showOrangeLevel3: boolean;
  showOrangeLevel4: boolean;
  showGreenCells: boolean;
  showGreenLevel1: boolean;
  showGreenLevel3: boolean;
  showGreenLevel4: boolean;
  showBlueCells: boolean;
  showBlueLevel1: boolean;
  showBlueLevel3: boolean;
  showBlueLevel4: boolean;
  showPinkCells: boolean;
  showPinkLevel1: boolean;
  showPinkLevel3: boolean;
  showPinkLevel4: boolean;
  // 选中的分组
  selectedRedGroups: Set<number>;
  selectedCyanGroups: Set<number>;
  selectedYellowGroups: Set<number>;
  selectedPurpleGroups: Set<number>;
  selectedOrangeGroups: Set<number>;
  selectedGreenGroups: Set<number>;
  selectedBlueGroups: Set<number>;
  selectedPinkGroups: Set<number>;
}

// 混合模式版本类型
export interface MixedModeVersion extends VersionBase {
  // 混合模式特定状态
  [key: string]: unknown;
}

// 矩阵模式版本类型
export interface MatrixModeVersion extends VersionBase {
  // 矩阵模式特定状态
  [key: string]: unknown;
}

// 版本管理器类型
export type VersionMap<T> = Record<string, T>;

export interface VersionState {
  savedVersions: VersionMap<DefaultVersion>;
  groupModeVersions: VersionMap<GroupModeVersion>;
  mixedModeVersions: VersionMap<MixedModeVersion>;
  matrixVersions: VersionMap<MatrixModeVersion>;
  currentVersion: string;
  currentMatrixVersion: string;
} 