export type ColorType = 
  | 'black' 
  | 'red' 
  | 'cyan' 
  | 'yellow' 
  | 'purple' 
  | 'orange' 
  | 'green' 
  | 'blue' 
  | 'pink';

export interface CoordinateGroup {
  coords: number[];  // 修改为更灵活的数组类型
  group: number | null;
}

export interface ColorCoordinates {
  level1: CoordinateGroup[];
  level2?: CoordinateGroup[];  // 某些颜色可能没有level2
  level3: CoordinateGroup[];
  level4: CoordinateGroup[];
}

// 统一颜色信息接口
export interface ColorInfo {
  exists: boolean;
  level: 1 | 2 | 3 | 4;
  group: number | null;
  colorType: ColorType;
}

// 全量颜色信息接口
export interface AllColorInfo {
  black?: { exists: boolean; letter: string };
  red?: ColorInfo;
  orange?: ColorInfo;
  cyan?: ColorInfo;
  yellow?: ColorInfo;
  purple?: ColorInfo;
  green?: ColorInfo;
  blue?: ColorInfo;
  pink?: ColorInfo;
}

// 颜色显示控制状态
export interface ColorDisplayState {
  showCells: boolean;
  showLevel1: boolean;
  showLevel2: boolean;
  showLevel3: boolean;
  showLevel4: boolean;
  selectedGroups: Set<number>;
} 