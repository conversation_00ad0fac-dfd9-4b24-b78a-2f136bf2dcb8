/**
 * 基础数据Store - 管理8种颜色的核心数据和网格数据
 * 🎯 核心职责：颜色坐标、颜色值、可见性控制、级别规则、网格数据管理
 * 📦 整合常量：颜色坐标数据、颜色配置、级别规则、网格尺寸等
 * 🔄 整合内容：原gridStore.ts的网格数据管理功能
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { ColorCoordinates } from '../types/color';
import type { CellData } from '../types/grid';

// 基础颜色类型定义
export type BasicColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';

// 颜色级别常量
export const COLOR_LEVELS = [1, 2, 3, 4] as const;
export type ColorLevel = typeof COLOR_LEVELS[number];

// 颜色可见性接口 - Debug-3.1修复: 灵活支持不同颜色的可用级别
export interface ColorVisibility {
  showCells: boolean;
  showLevel1?: boolean; // 根据AVAILABLE_LEVELS动态设置
  showLevel2?: boolean; // 根据AVAILABLE_LEVELS动态设置
  showLevel3?: boolean; // 根据AVAILABLE_LEVELS动态设置
  showLevel4?: boolean; // 根据AVAILABLE_LEVELS动态设置
}

// 颜色值定义
export interface ColorValue {
  name: string;
  hex: string;
  rgb: [number, number, number];
  hsl: [number, number, number];
}

// 黑色格子数据
export interface BlackCellData {
  coordinates: { coords: [number, number]; letter: string }[];
  visibility: boolean;
}

// 可用级别映射
export const AVAILABLE_LEVELS: Record<BasicColorType, number[]> = {
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
} as const;

// 颜色坐标数据 - 红色
const redCoordinates: ColorCoordinates = {
    level1: [
        { coords: [8, 0], group: null },
        { coords: [-8, 16], group: null },
        { coords: [-8, -16], group: null },
    ],
    level2: [
        { coords: [4, 0], group: 1 },
        { coords: [-12, 0], group: 2 },
        { coords: [-4, 8], group: 3 },
        { coords: [-4, -8], group: 4 },
        { coords: [12, 8], group: 5 },
        { coords: [12, -8], group: 6 },
        { coords: [-12, 16], group: 7 },
        { coords: [4, 16], group: 8 },
        { coords: [-12, -16], group: 9 },
        { coords: [4, -16], group: 10 },
    ],
    level3: [
        // 组➊
        { coords: [2, 0], group: 1 },
        { coords: [6, 0], group: 1 },
        { coords: [4, 2], group: 1 },
        { coords: [4, -2], group: 1 },
        // 组➋
        { coords: [-10, 0], group: 2 },
        { coords: [-14, 0], group: 2 },
        { coords: [-12, 2], group: 2 },
        { coords: [-12, -2], group: 2 },
        // 组➌
        { coords: [-6, 8], group: 3 },
        { coords: [-4, 6], group: 3 },
        { coords: [-4, 10], group: 3 },
        { coords: [-2, 8], group: 3 },
        // 组➍
        { coords: [-6, -8], group: 4 },
        { coords: [-4, -6], group: 4 },
        { coords: [-4, -10], group: 4 },
        { coords: [-2, -8], group: 4 },
        // 组➎
        { coords: [12, 6], group: 5 },
        { coords: [14, 8], group: 5 },
        { coords: [10, 8], group: 5 },
        { coords: [12, 10], group: 5 },
        // 组➏
        { coords: [10, -8], group: 6 },
        { coords: [12, -6], group: 6 },
        { coords: [12, -10], group: 6 },
        { coords: [14, -8], group: 6 },
        // 组➐
        { coords: [-12, 14], group: 7 },
        { coords: [-10, 16], group: 7 },
        { coords: [-14, 16], group: 7 },
        // 组➑
        { coords: [4, 14], group: 8 },
        { coords: [2, 16], group: 8 },
        { coords: [6, 16], group: 8 },
        // 组➒
        { coords: [-12, -14], group: 9 },
        { coords: [-10, -16], group: 9 },
        { coords: [-14, -16], group: 9 },
        // 组➓
        { coords: [4, -14], group: 10 },
        { coords: [2, -16], group: 10 },
        { coords: [6, -16], group: 10 },
    ],
    level4: [
        // 组➊
        { coords: [1, 0], group: 1 },
        { coords: [3, 0], group: 1 },
        { coords: [5, 0], group: 1 },
        { coords: [7, 0], group: 1 },
        { coords: [2, 1], group: 1 },
        { coords: [2, -1], group: 1 },
        { coords: [3, 2], group: 1 },
        { coords: [3, -2], group: 1 },
        { coords: [4, 1], group: 1 },
        { coords: [4, 3], group: 1 },
        { coords: [4, -1], group: 1 },
        { coords: [4, -3], group: 1 },
        { coords: [6, 1], group: 1 },
        { coords: [6, -1], group: 1 },
        { coords: [5, 2], group: 1 },
        { coords: [5, -2], group: 1 },
        // 组➋
        { coords: [-15, 0], group: 2 },
        { coords: [-13, 0], group: 2 },
        { coords: [-11, 0], group: 2 },
        { coords: [-9, 0], group: 2 },
        { coords: [-14, 1], group: 2 },
        { coords: [-14, -1], group: 2 },
        { coords: [-13, 2], group: 2 },
        { coords: [-13, -2], group: 2 },
        { coords: [-12, 1], group: 2 },
        { coords: [-12, 3], group: 2 },
        { coords: [-12, -1], group: 2 },
        { coords: [-12, -3], group: 2 },
        { coords: [-10, 1], group: 2 },
        { coords: [-10, -1], group: 2 },
        { coords: [-11, 2], group: 2 },
        { coords: [-11, -2], group: 2 },
        // 组➌
        { coords: [-7, 8], group: 3 },
        { coords: [-5, 8], group: 3 },
        { coords: [-3, 8], group: 3 },
        { coords: [-1, 8], group: 3 },
        { coords: [-6, 9], group: 3 },
        { coords: [-6, 7], group: 3 },
        { coords: [-5, 10], group: 3 },
        { coords: [-5, 6], group: 3 },
        { coords: [-4, 9], group: 3 },
        { coords: [-4, 11], group: 3 },
        { coords: [-4, 7], group: 3 },
        { coords: [-4, 5], group: 3 },
        { coords: [-2, 9], group: 3 },
        { coords: [-2, 7], group: 3 },
        { coords: [-3, 10], group: 3 },
        { coords: [-3, 6], group: 3 },
        // 组➍
        { coords: [-7, -8], group: 4 },
        { coords: [-5, -8], group: 4 },
        { coords: [-3, -8], group: 4 },
        { coords: [-1, -8], group: 4 },
        { coords: [-6, -7], group: 4 },
        { coords: [-6, -9], group: 4 },
        { coords: [-5, -6], group: 4 },
        { coords: [-5, -10], group: 4 },
        { coords: [-4, -7], group: 4 },
        { coords: [-4, -5], group: 4 },
        { coords: [-4, -9], group: 4 },
        { coords: [-4, -11], group: 4 },
        { coords: [-2, -7], group: 4 },
        { coords: [-2, -9], group: 4 },
        { coords: [-3, -6], group: 4 },
        { coords: [-3, -10], group: 4 },
        // 组➎
        { coords: [9, 8], group: 5 },
        { coords: [11, 8], group: 5 },
        { coords: [13, 8], group: 5 },
        { coords: [15, 8], group: 5 },
        { coords: [10, 9], group: 5 },
        { coords: [10, 7], group: 5 },
        { coords: [11, 10], group: 5 },
        { coords: [11, 6], group: 5 },
        { coords: [12, 9], group: 5 },
        { coords: [12, 11], group: 5 },
        { coords: [12, 7], group: 5 },
        { coords: [12, 5], group: 5 },
        { coords: [14, 9], group: 5 },
        { coords: [14, 7], group: 5 },
        { coords: [13, 10], group: 5 },
        { coords: [13, 6], group: 5 },
        // 组➏
        { coords: [9, -8], group: 6 },
        { coords: [11, -8], group: 6 },
        { coords: [13, -8], group: 6 },
        { coords: [15, -8], group: 6 },
        { coords: [10, -7], group: 6 },
        { coords: [10, -9], group: 6 },
        { coords: [11, -6], group: 6 },
        { coords: [11, -10], group: 6 },
        { coords: [12, -7], group: 6 },
        { coords: [12, -5], group: 6 },
        { coords: [12, -9], group: 6 },
        { coords: [12, -11], group: 6 },
        { coords: [14, -7], group: 6 },
        { coords: [14, -9], group: 6 },
        { coords: [13, -6], group: 6 },
        { coords: [13, -10], group: 6 },
        // 组➐
        { coords: [-15, 16], group: 7 },
        { coords: [-14, 15], group: 7 },
        { coords: [-13, 16], group: 7 },
        { coords: [-13, 14], group: 7 },
        { coords: [-12, 15], group: 7 },
        { coords: [-12, 13], group: 7 },
        { coords: [-11, 16], group: 7 },
        { coords: [-11, 14], group: 7 },
        { coords: [-10, 15], group: 7 },
        { coords: [-9, 16], group: 7 },
        // 组➑
        { coords: [1, 16], group: 8 },
        { coords: [2, 15], group: 8 },
        { coords: [3, 16], group: 8 },
        { coords: [3, 14], group: 8 },
        { coords: [4, 15], group: 8 },
        { coords: [4, 13], group: 8 },
        { coords: [5, 16], group: 8 },
        { coords: [5, 14], group: 8 },
        { coords: [6, 15], group: 8 },
        { coords: [7, 16], group: 8 },
        // 组➒
        { coords: [-15, -16], group: 9 },
        { coords: [-14, -15], group: 9 },
        { coords: [-13, -16], group: 9 },
        { coords: [-13, -14], group: 9 },
        { coords: [-12, -15], group: 9 },
        { coords: [-12, -13], group: 9 },
        { coords: [-11, -16], group: 9 },
        { coords: [-11, -14], group: 9 },
        { coords: [-10, -15], group: 9 },
        { coords: [-9, -16], group: 9 },
        // 组➓
        { coords: [1, -16], group: 10 },
        { coords: [2, -15], group: 10 },
        { coords: [3, -16], group: 10 },
        { coords: [3, -14], group: 10 },
        { coords: [4, -15], group: 10 },
        { coords: [4, -13], group: 10 },
        { coords: [5, -16], group: 10 },
        { coords: [5, -14], group: 10 },
        { coords: [6, -15], group: 10 },
        { coords: [7, -16], group: 10 },
    ],
};

// 颜色坐标数据 - 青色
const cyanCoordinates: ColorCoordinates = {
    level1: [
        { coords: [-8, 0], group: null },
        { coords: [8, 16], group: null },
        { coords: [8, -16], group: null },
    ],
    level2: [
        { coords: [12, 0], group: 1 },
        { coords: [-4, 0], group: 2 },
        { coords: [-12, 8], group: 3 },
        { coords: [-12, -8], group: 4 },
        { coords: [4, 8], group: 5 },
        { coords: [4, -8], group: 6 },
        { coords: [-4, 16], group: 7 },
        { coords: [12, 16], group: 8 },
        { coords: [-4, -16], group: 9 },
        { coords: [12, -16], group: 10 },
    ],
    level3: [
        // 组➊
        { coords: [10, 0], group: 1 },
        { coords: [14, 0], group: 1 },
        { coords: [12, 2], group: 1 },
        { coords: [12, -2], group: 1 },
        // 组➋
        { coords: [-2, 0], group: 2 },
        { coords: [-6, 0], group: 2 },
        { coords: [-4, 2], group: 2 },
        { coords: [-4, -2], group: 2 },
        // 组➌
        { coords: [-14, 8], group: 3 },
        { coords: [-12, 6], group: 3 },
        { coords: [-12, 10], group: 3 },
        { coords: [-10, 8], group: 3 },
        // 组➍
        { coords: [-14, -8], group: 4 },
        { coords: [-12, -6], group: 4 },
        { coords: [-12, -10], group: 4 },
        { coords: [-10, -8], group: 4 },
        // 组➎
        { coords: [4, 6], group: 5 },
        { coords: [6, 8], group: 5 },
        { coords: [2, 8], group: 5 },
        { coords: [4, 10], group: 5 },
        // 组➏
        { coords: [2, -8], group: 6 },
        { coords: [4, -6], group: 6 },
        { coords: [4, -10], group: 6 },
        { coords: [6, -8], group: 6 },
        // 组➐
        { coords: [-4, 14], group: 7 },
        { coords: [-2, 16], group: 7 },
        { coords: [-6, 16], group: 7 },
        // 组➑
        { coords: [12, 14], group: 8 },
        { coords: [10, 16], group: 8 },
        { coords: [14, 16], group: 8 },
        // 组➒
        { coords: [-4, -14], group: 9 },
        { coords: [-2, -16], group: 9 },
        { coords: [-6, -16], group: 9 },
        // 组➓
        { coords: [12, -14], group: 10 },
        { coords: [10, -16], group: 10 },
        { coords: [14, -16], group: 10 },
    ],
    level4: [
        // 组➊
        { coords: [9, 0], group: 1 },
        { coords: [11, 0], group: 1 },
        { coords: [13, 0], group: 1 },
        { coords: [15, 0], group: 1 },
        { coords: [10, 1], group: 1 },
        { coords: [10, -1], group: 1 },
        { coords: [11, 2], group: 1 },
        { coords: [11, -2], group: 1 },
        { coords: [12, 1], group: 1 },
        { coords: [12, 3], group: 1 },
        { coords: [12, -1], group: 1 },
        { coords: [12, -3], group: 1 },
        { coords: [14, 1], group: 1 },
        { coords: [14, -1], group: 1 },
        { coords: [13, 2], group: 1 },
        { coords: [13, -2], group: 1 },
        // 组➋
        { coords: [-7, 0], group: 2 },
        { coords: [-5, 0], group: 2 },
        { coords: [-3, 0], group: 2 },
        { coords: [-1, 0], group: 2 },
        { coords: [-6, 1], group: 2 },
        { coords: [-6, -1], group: 2 },
        { coords: [-5, 2], group: 2 },
        { coords: [-5, -2], group: 2 },
        { coords: [-4, 1], group: 2 },
        { coords: [-4, 3], group: 2 },
        { coords: [-4, -1], group: 2 },
        { coords: [-4, -3], group: 2 },
        { coords: [-2, 1], group: 2 },
        { coords: [-2, -1], group: 2 },
        { coords: [-3, 2], group: 2 },
        { coords: [-3, -2], group: 2 },
        // 组➌
        { coords: [-15, 8], group: 3 },
        { coords: [-13, 8], group: 3 },
        { coords: [-11, 8], group: 3 },
        { coords: [-9, 8], group: 3 },
        { coords: [-14, 9], group: 3 },
        { coords: [-14, 7], group: 3 },
        { coords: [-13, 10], group: 3 },
        { coords: [-13, 6], group: 3 },
        { coords: [-12, 9], group: 3 },
        { coords: [-12, 11], group: 3 },
        { coords: [-12, 7], group: 3 },
        { coords: [-12, 5], group: 3 },
        { coords: [-10, 9], group: 3 },
        { coords: [-10, 7], group: 3 },
        { coords: [-11, 10], group: 3 },
        { coords: [-11, 6], group: 3 },
        // 组➍
        { coords: [-15, -8], group: 4 },
        { coords: [-13, -8], group: 4 },
        { coords: [-11, -8], group: 4 },
        { coords: [-9, -8], group: 4 },
        { coords: [-14, -7], group: 4 },
        { coords: [-14, -9], group: 4 },
        { coords: [-13, -6], group: 4 },
        { coords: [-13, -10], group: 4 },
        { coords: [-12, -7], group: 4 },
        { coords: [-12, -5], group: 4 },
        { coords: [-12, -9], group: 4 },
        { coords: [-12, -11], group: 4 },
        { coords: [-10, -7], group: 4 },
        { coords: [-10, -9], group: 4 },
        { coords: [-11, -6], group: 4 },
        { coords: [-11, -10], group: 4 },
        // 组➎
        { coords: [1, 8], group: 5 },
        { coords: [3, 8], group: 5 },
        { coords: [5, 8], group: 5 },
        { coords: [7, 8], group: 5 },
        { coords: [2, 9], group: 5 },
        { coords: [2, 7], group: 5 },
        { coords: [3, 10], group: 5 },
        { coords: [3, 6], group: 5 },
        { coords: [4, 9], group: 5 },
        { coords: [4, 11], group: 5 },
        { coords: [4, 7], group: 5 },
        { coords: [4, 5], group: 5 },
        { coords: [6, 9], group: 5 },
        { coords: [6, 7], group: 5 },
        { coords: [5, 10], group: 5 },
        { coords: [5, 6], group: 5 },
        // 组➏
        { coords: [1, -8], group: 6 },
        { coords: [3, -8], group: 6 },
        { coords: [5, -8], group: 6 },
        { coords: [7, -8], group: 6 },
        { coords: [2, -7], group: 6 },
        { coords: [2, -9], group: 6 },
        { coords: [3, -6], group: 6 },
        { coords: [3, -10], group: 6 },
        { coords: [4, -7], group: 6 },
        { coords: [4, -5], group: 6 },
        { coords: [4, -9], group: 6 },
        { coords: [4, -11], group: 6 },
        { coords: [6, -7], group: 6 },
        { coords: [6, -9], group: 6 },
        { coords: [5, -6], group: 6 },
        { coords: [5, -10], group: 6 },
        // 组➐
        { coords: [-7, 16], group: 7 },
        { coords: [-6, 15], group: 7 },
        { coords: [-5, 16], group: 7 },
        { coords: [-5, 14], group: 7 },
        { coords: [-4, 15], group: 7 },
        { coords: [-4, 13], group: 7 },
        { coords: [-3, 16], group: 7 },
        { coords: [-3, 14], group: 7 },
        { coords: [-2, 15], group: 7 },
        { coords: [-1, 16], group: 7 },
        // 组➑
        { coords: [9, 16], group: 8 },
        { coords: [10, 15], group: 8 },
        { coords: [11, 16], group: 8 },
        { coords: [11, 14], group: 8 },
        { coords: [12, 15], group: 8 },
        { coords: [12, 13], group: 8 },
        { coords: [13, 16], group: 8 },
        { coords: [13, 14], group: 8 },
        { coords: [14, 15], group: 8 },
        { coords: [15, 16], group: 8 },
        // 组➒
        { coords: [-7, -16], group: 9 },
        { coords: [-6, -15], group: 9 },
        { coords: [-5, -16], group: 9 },
        { coords: [-5, -14], group: 9 },
        { coords: [-4, -15], group: 9 },
        { coords: [-4, -13], group: 9 },
        { coords: [-3, -16], group: 9 },
        { coords: [-3, -14], group: 9 },
        { coords: [-2, -15], group: 9 },
        { coords: [-1, -16], group: 9 },
        // 组➓
        { coords: [9, -16], group: 10 },
        { coords: [10, -15], group: 10 },
        { coords: [11, -16], group: 10 },
        { coords: [11, -14], group: 10 },
        { coords: [12, -15], group: 10 },
        { coords: [12, -13], group: 10 },
        { coords: [13, -16], group: 10 },
        { coords: [13, -14], group: 10 },
        { coords: [14, -15], group: 10 },
        { coords: [15, -16], group: 10 },
    ],
};

// 黄色格子坐标映射
const yellowCoordinates: ColorCoordinates = {
    level1: [
        { coords: [0, -8], group: null },
        { coords: [16, 8], group: null },
        { coords: [-16, 8], group: null },
    ],
    level2: [
        { coords: [0, -4], group: 1 },
        { coords: [-4, -16], group: 2 },
        { coords: [-8, 4], group: 3 },
        { coords: [-8, -12], group: 4 },
        { coords: [8, 4], group: 5 },
        { coords: [8, -12], group: 6 },
        { coords: [-16, 8], group: 7 },
        { coords: [0, 12], group: 8 },
        { coords: [16, 12], group: 9 },
        { coords: [-16, 12], group: 7 },
        { coords: [16, -4], group: 10 },
        { coords: [-16, -4], group: 2 },
    ],
    level3: [
        // 组➊
        { coords: [-2, -4], group: 1 },
        { coords: [2, -4], group: 1 },
        { coords: [0, -2], group: 1 },
        { coords: [0, -6], group: 1 },
        // 组➋
        { coords: [-16, -2], group: 2 },
        { coords: [-16, -6], group: 2 },
        { coords: [-14, -4], group: 2 },
        // 组➌
        { coords: [-10, 4], group: 3 },
        { coords: [-8, 2], group: 3 },
        { coords: [-8, 6], group: 3 },
        { coords: [-6, 4], group: 3 },
        // 组➍
        { coords: [-10, -12], group: 4 },
        { coords: [-8, -10], group: 4 },
        { coords: [-8, -14], group: 4 },
        { coords: [-6, -12], group: 4 },
        // 组➎
        { coords: [8, 2], group: 5 },
        { coords: [10, 4], group: 5 },
        { coords: [6, 4], group: 5 },
        { coords: [8, 6], group: 5 },
        // 组➏
        { coords: [6, -12], group: 6 },
        { coords: [8, -10], group: 6 },
        { coords: [8, -14], group: 6 },
        { coords: [10, -12], group: 6 },
        // 组➐
        { coords: [-16, 10], group: 7 },
        { coords: [-16, 14], group: 7 },
        { coords: [-14, 12], group: 7 },
        // 组➑
        { coords: [0, 10], group: 8 },
        { coords: [0, 14], group: 8 },
        { coords: [-2, 12], group: 8 },
        { coords: [2, 12], group: 8 },
        // 组➒
        { coords: [16, 10], group: 9 },
        { coords: [16, 14], group: 9 },
        { coords: [14, 12], group: 9 },
        // 组➓
        { coords: [16, -2], group: 10 },
        { coords: [16, -6], group: 10 },
        { coords: [14, -4], group: 10 },
    ],
    level4: [
        // 组➊
        { coords: [-3, -4], group: 1 },
        { coords: [-1, -4], group: 1 },
        { coords: [1, -4], group: 1 },
        { coords: [3, -4], group: 1 },
        { coords: [-2, -3], group: 1 },
        { coords: [-2, -5], group: 1 },
        { coords: [-1, -2], group: 1 },
        { coords: [-1, -6], group: 1 },
        { coords: [0, -3], group: 1 },
        { coords: [0, -1], group: 1 },
        { coords: [0, -5], group: 1 },
        { coords: [0, -7], group: 1 },
        { coords: [2, -3], group: 1 },
        { coords: [2, -5], group: 1 },
        { coords: [1, -2], group: 1 },
        { coords: [1, -6], group: 1 },
        // 组➋
        { coords: [-16, -1], group: 2 },
        { coords: [-16, -3], group: 2 },
        { coords: [-16, -5], group: 2 },
        { coords: [-16, -7], group: 2 },
        { coords: [-15, -2], group: 2 },
        { coords: [-15, -4], group: 2 },
        { coords: [-15, -6], group: 2 },
        { coords: [-14, -3], group: 2 },
        { coords: [-14, -5], group: 2 },
        { coords: [-13, -4], group: 2 },
        // 组➌
        { coords: [-11, 4], group: 3 },
        { coords: [-9, 4], group: 3 },
        { coords: [-7, 4], group: 3 },
        { coords: [-5, 4], group: 3 },
        { coords: [-10, 5], group: 3 },
        { coords: [-10, 3], group: 3 },
        { coords: [-9, 6], group: 3 },
        { coords: [-9, 2], group: 3 },
        { coords: [-8, 5], group: 3 },
        { coords: [-8, 7], group: 3 },
        { coords: [-8, 3], group: 3 },
        { coords: [-8, 1], group: 3 },
        { coords: [-6, 5], group: 3 },
        { coords: [-6, 3], group: 3 },
        { coords: [-7, 6], group: 3 },
        { coords: [-7, 2], group: 3 },
        // 组➍
        { coords: [-11, -12], group: 4 },
        { coords: [-9, -12], group: 4 },
        { coords: [-7, -12], group: 4 },
        { coords: [-5, -12], group: 4 },
        { coords: [-10, -11], group: 4 },
        { coords: [-10, -13], group: 4 },
        { coords: [-9, -10], group: 4 },
        { coords: [-9, -14], group: 4 },
        { coords: [-8, -11], group: 4 },
        { coords: [-8, -9], group: 4 },
        { coords: [-8, -13], group: 4 },
        { coords: [-8, -15], group: 4 },
        { coords: [-6, -11], group: 4 },
        { coords: [-6, -13], group: 4 },
        { coords: [-7, -10], group: 4 },
        { coords: [-7, -14], group: 4 },
        // 组➎
        { coords: [5, 4], group: 5 },
        { coords: [7, 4], group: 5 },
        { coords: [9, 4], group: 5 },
        { coords: [11, 4], group: 5 },
        { coords: [6, 5], group: 5 },
        { coords: [6, 3], group: 5 },
        { coords: [7, 6], group: 5 },
        { coords: [7, 2], group: 5 },
        { coords: [8, 5], group: 5 },
        { coords: [8, 7], group: 5 },
        { coords: [8, 3], group: 5 },
        { coords: [8, 1], group: 5 },
        { coords: [10, 5], group: 5 },
        { coords: [10, 3], group: 5 },
        { coords: [9, 6], group: 5 },
        { coords: [9, 2], group: 5 },
        // 组➏
        { coords: [5, -12], group: 6 },
        { coords: [7, -12], group: 6 },
        { coords: [9, -12], group: 6 },
        { coords: [11, -12], group: 6 },
        { coords: [6, -11], group: 6 },
        { coords: [6, -13], group: 6 },
        { coords: [7, -10], group: 6 },
        { coords: [7, -14], group: 6 },
        { coords: [8, -11], group: 6 },
        { coords: [8, -9], group: 6 },
        { coords: [8, -13], group: 6 },
        { coords: [8, -15], group: 6 },
        { coords: [10, -11], group: 6 },
        { coords: [10, -13], group: 6 },
        { coords: [9, -10], group: 6 },
        { coords: [9, -14], group: 6 },
        // 组➐
        { coords: [-16, 15], group: 7 },
        { coords: [-16, 13], group: 7 },
        { coords: [-16, 11], group: 7 },
        { coords: [-16, 9], group: 7 },
        { coords: [-15, 14], group: 7 },
        { coords: [-15, 12], group: 7 },
        { coords: [-15, 10], group: 7 },
        { coords: [-14, 13], group: 7 },
        { coords: [-14, 11], group: 7 },
        { coords: [-13, 12], group: 7 },
        // 组➑
        { coords: [-3, 12], group: 8 },
        { coords: [-1, 12], group: 8 },
        { coords: [1, 12], group: 8 },
        { coords: [3, 12], group: 8 },
        { coords: [-2, 13], group: 8 },
        { coords: [-2, 11], group: 8 },
        { coords: [-1, 14], group: 8 },
        { coords: [-1, 10], group: 8 },
        { coords: [0, 13], group: 8 },
        { coords: [0, 15], group: 8 },
        { coords: [0, 11], group: 8 },
        { coords: [0, 9], group: 8 },
        { coords: [2, 13], group: 8 },
        { coords: [2, 11], group: 8 },
        { coords: [1, 14], group: 8 },
        { coords: [1, 10], group: 8 },
        // 组➒
        { coords: [16, 9], group: 9 },
        { coords: [16, 11], group: 9 },
        { coords: [16, 13], group: 9 },
        { coords: [16, 15], group: 9 },
        { coords: [15, 10], group: 9 },
        { coords: [15, 12], group: 9 },
        { coords: [15, 14], group: 9 },
        { coords: [14, 11], group: 9 },
        { coords: [14, 13], group: 9 },
        { coords: [13, 12], group: 9 },
        // 组➓
        { coords: [16, -7], group: 10 },
        { coords: [16, -5], group: 10 },
        { coords: [16, -3], group: 10 },
        { coords: [16, -1], group: 10 },
        { coords: [15, -6], group: 10 },
        { coords: [15, -4], group: 10 },
        { coords: [15, -2], group: 10 },
        { coords: [14, -5], group: 10 },
        { coords: [14, -3], group: 10 },
        { coords: [13, -4], group: 10 },
    ],
};

// 紫色格子坐标映射
const purpleCoordinates: ColorCoordinates = {
    level1: [
        { coords: [0, 8], group: null },
        { coords: [16, -8], group: null },
        { coords: [-16, -8], group: null },
    ],
    level2: [
        { coords: [0, -12], group: 1 },
        { coords: [-8, 12], group: 3 },
        { coords: [-8, -4], group: 4 },
        { coords: [8, 12], group: 5 },
        { coords: [8, -4], group: 6 },
        { coords: [-16, 0], group: 7 },
        { coords: [0, 4], group: 8 },
        { coords: [16, 4], group: 9 },
        { coords: [-16, 4], group: 7 },
        { coords: [16, -12], group: 10 },
        { coords: [-16, -12], group: 2 },
    ],
    level3: [
        // 组➊
        { coords: [-2, -12], group: 1 },
        { coords: [2, -12], group: 1 },
        { coords: [0, -10], group: 1 },
        { coords: [0, -14], group: 1 },
        // 组➋
        { coords: [-16, -10], group: 2 },
        { coords: [-16, -14], group: 2 },
        { coords: [-14, -12], group: 2 },
        // 组➌
        { coords: [-10, 12], group: 3 },
        { coords: [-8, 10], group: 3 },
        { coords: [-8, 14], group: 3 },
        { coords: [-6, 12], group: 3 },
        // 组➍
        { coords: [-10, -4], group: 4 },
        { coords: [-8, -2], group: 4 },
        { coords: [-8, -6], group: 4 },
        { coords: [-6, -4], group: 4 },
        // 组➎
        { coords: [8, 10], group: 5 },
        { coords: [10, 12], group: 5 },
        { coords: [6, 12], group: 5 },
        { coords: [8, 14], group: 5 },
        // 组➏
        { coords: [6, -4], group: 6 },
        { coords: [8, -2], group: 6 },
        { coords: [8, -6], group: 6 },
        { coords: [10, -4], group: 6 },
        // 组➐
        { coords: [-16, 2], group: 7 },
        { coords: [-16, 6], group: 7 },
        { coords: [-14, 4], group: 7 },
        // 组➑
        { coords: [0, 2], group: 8 },
        { coords: [0, 6], group: 8 },
        { coords: [-2, 4], group: 8 },
        { coords: [2, 4], group: 8 },
        // 组➒
        { coords: [16, 2], group: 9 },
        { coords: [16, 6], group: 9 },
        { coords: [14, 4], group: 9 },
        // 组➓
        { coords: [16, -10], group: 10 },
        { coords: [16, -14], group: 10 },
        { coords: [14, -12], group: 10 },
    ],
    level4: [
        // 组➊
        { coords: [-3, -12], group: 1 },
        { coords: [-1, -12], group: 1 },
        { coords: [1, -12], group: 1 },
        { coords: [3, -12], group: 1 },
        { coords: [-2, -11], group: 1 },
        { coords: [-2, -13], group: 1 },
        { coords: [-1, -10], group: 1 },
        { coords: [-1, -14], group: 1 },
        { coords: [0, -11], group: 1 },
        { coords: [0, -9], group: 1 },
        { coords: [0, -13], group: 1 },
        { coords: [0, -15], group: 1 },
        { coords: [2, -11], group: 1 },
        { coords: [2, -13], group: 1 },
        { coords: [1, -10], group: 1 },
        { coords: [1, -14], group: 1 },
        // 组➋
        { coords: [-16, -9], group: 2 },
        { coords: [-16, -11], group: 2 },
        { coords: [-16, -13], group: 2 },
        { coords: [-16, -15], group: 2 },
        { coords: [-15, -10], group: 2 },
        { coords: [-15, -12], group: 2 },
        { coords: [-15, -14], group: 2 },
        { coords: [-14, -11], group: 2 },
        { coords: [-14, -13], group: 2 },
        { coords: [-13, -12], group: 2 },
        // 组➌
        { coords: [-11, 12], group: 3 },
        { coords: [-9, 12], group: 3 },
        { coords: [-7, 12], group: 3 },
        { coords: [-5, 12], group: 3 },
        { coords: [-10, 13], group: 3 },
        { coords: [-10, 11], group: 3 },
        { coords: [-9, 14], group: 3 },
        { coords: [-9, 10], group: 3 },
        { coords: [-8, 13], group: 3 },
        { coords: [-8, 15], group: 3 },
        { coords: [-8, 11], group: 3 },
        { coords: [-8, 9], group: 3 },
        { coords: [-6, 13], group: 3 },
        { coords: [-6, 11], group: 3 },
        { coords: [-7, 14], group: 3 },
        { coords: [-7, 10], group: 3 },
        // 组➍
        { coords: [-11, -4], group: 4 },
        { coords: [-9, -4], group: 4 },
        { coords: [-7, -4], group: 4 },
        { coords: [-5, -4], group: 4 },
        { coords: [-10, -3], group: 4 },
        { coords: [-10, -5], group: 4 },
        { coords: [-9, -2], group: 4 },
        { coords: [-9, -6], group: 4 },
        { coords: [-8, -3], group: 4 },
        { coords: [-8, -1], group: 4 },
        { coords: [-8, -5], group: 4 },
        { coords: [-8, -7], group: 4 },
        { coords: [-6, -3], group: 4 },
        { coords: [-6, -5], group: 4 },
        { coords: [-7, -2], group: 4 },
        { coords: [-7, -6], group: 4 },
        // 组➎
        { coords: [5, 12], group: 5 },
        { coords: [7, 12], group: 5 },
        { coords: [9, 12], group: 5 },
        { coords: [11, 12], group: 5 },
        { coords: [6, 13], group: 5 },
        { coords: [6, 11], group: 5 },
        { coords: [7, 14], group: 5 },
        { coords: [7, 10], group: 5 },
        { coords: [8, 13], group: 5 },
        { coords: [8, 15], group: 5 },
        { coords: [8, 11], group: 5 },
        { coords: [8, 9], group: 5 },
        { coords: [10, 13], group: 5 },
        { coords: [10, 11], group: 5 },
        { coords: [9, 14], group: 5 },
        { coords: [9, 10], group: 5 },
        // 组➏
        { coords: [5, -4], group: 6 },
        { coords: [7, -4], group: 6 },
        { coords: [9, -4], group: 6 },
        { coords: [11, -4], group: 6 },
        { coords: [6, -3], group: 6 },
        { coords: [6, -5], group: 6 },
        { coords: [7, -2], group: 6 },
        { coords: [7, -6], group: 6 },
        { coords: [8, -3], group: 6 },
        { coords: [8, -1], group: 6 },
        { coords: [8, -5], group: 6 },
        { coords: [8, -7], group: 6 },
        { coords: [10, -3], group: 6 },
        { coords: [10, -5], group: 6 },
        { coords: [9, -2], group: 6 },
        { coords: [9, -6], group: 6 },
        // 组➐
        { coords: [-16, 7], group: 7 },
        { coords: [-16, 5], group: 7 },
        { coords: [-16, 3], group: 7 },
        { coords: [-16, 1], group: 7 },
        { coords: [-15, 6], group: 7 },
        { coords: [-15, 4], group: 7 },
        { coords: [-15, 2], group: 7 },
        { coords: [-14, 5], group: 7 },
        { coords: [-14, 3], group: 7 },
        { coords: [-13, 4], group: 7 },
        // 组➑
        { coords: [-3, 4], group: 8 },
        { coords: [-1, 4], group: 8 },
        { coords: [1, 4], group: 8 },
        { coords: [3, 4], group: 8 },
        { coords: [-2, 5], group: 8 },
        { coords: [-2, 3], group: 8 },
        { coords: [-1, 6], group: 8 },
        { coords: [-1, 2], group: 8 },
        { coords: [0, 5], group: 8 },
        { coords: [0, 7], group: 8 },
        { coords: [0, 3], group: 8 },
        { coords: [0, 1], group: 8 },
        { coords: [2, 5], group: 8 },
        { coords: [2, 3], group: 8 },
        { coords: [1, 6], group: 8 },
        { coords: [1, 2], group: 8 },
        // 组➒
        { coords: [16, 1], group: 9 },
        { coords: [16, 3], group: 9 },
        { coords: [16, 5], group: 9 },
        { coords: [16, 7], group: 9 },
        { coords: [15, 2], group: 9 },
        { coords: [15, 4], group: 9 },
        { coords: [15, 6], group: 9 },
        { coords: [14, 3], group: 9 },
        { coords: [14, 5], group: 9 },
        { coords: [13, 4], group: 9 },
        // 组➓
        { coords: [16, -15], group: 10 },
        { coords: [16, -13], group: 10 },
        { coords: [16, -11], group: 10 },
        { coords: [16, -9], group: 10 },
        { coords: [15, -14], group: 10 },
        { coords: [15, -12], group: 10 },
        { coords: [15, -10], group: 10 },
        { coords: [14, -13], group: 10 },
        { coords: [14, -11], group: 10 },
        { coords: [13, -12], group: 10 },
    ],
};

// 橙色格子坐标映射
const orangeCoordinates: ColorCoordinates = {
    level1: [
        { coords: [-12, 12], group: 11 }, // 竖1横1
        { coords: [12, 4], group: 42 }, // 竖4横2
        { coords: [4, -4], group: 33 }, // 竖3横3
        { coords: [-4, -12], group: 24 }, // 竖2横4
    ],
    level3: [
        // 竖1横1
        { coords: [-14, 14], group: 11 },
        // 竖1横2
        { coords: [-10, 2], group: 12 },
        // 竖1横3
        { coords: [-14, -2], group: 13 },
        // 竖1横4
        { coords: [-10, -14], group: 14 },
        // 竖2横1
        { coords: [-2, 10], group: 21 },
        // 竖2横2
        { coords: [-6, 6], group: 22 },
        // 竖2横3
        { coords: [-2, -6], group: 23 },
        // 竖2横4
        { coords: [-6, -10], group: 24 },
        // 竖3横1
        { coords: [2, 14], group: 31 },
        // 竖3横2
        { coords: [6, 2], group: 32 },
        // 竖3横3
        { coords: [2, -2], group: 33 },
        // 竖3横4
        { coords: [6, -14], group: 34 },
        // 竖4横1
        { coords: [14, 10], group: 41 },
        // 竖4横2
        { coords: [10, 6], group: 42 },
        // 竖4横3
        { coords: [14, -6], group: 43 },
        // 竖4横4
        { coords: [10, -10], group: 44 },
    ],
    level4: [
        // 竖1横1
        { coords: [-15, 15], group: 11 },
        { coords: [-13, 15], group: 11 },
        { coords: [-13, 13], group: 11 },
        { coords: [-15, 13], group: 11 },
        // 竖1横2
        { coords: [-11, 3], group: 12 },
        { coords: [-9, 3], group: 12 },
        { coords: [-9, 1], group: 12 },
        { coords: [-11, 1], group: 12 },
        // 竖1横3
        { coords: [-15, -1], group: 13 },
        { coords: [-13, -1], group: 13 },
        { coords: [-13, -3], group: 13 },
        { coords: [-15, -3], group: 13 },
        // 竖1横4
        { coords: [-11, -13], group: 14 },
        { coords: [-9, -13], group: 14 },
        { coords: [-9, -15], group: 14 },
        { coords: [-11, -15], group: 14 },
        // 竖2横1
        { coords: [-3, 11], group: 21 },
        { coords: [-1, 11], group: 21 },
        { coords: [-1, 9], group: 21 },
        { coords: [-3, 9], group: 21 },
        // 竖2横2
        { coords: [-7, 7], group: 22 },
        { coords: [-5, 7], group: 22 },
        { coords: [-5, 5], group: 22 },
        { coords: [-7, 5], group: 22 },
        // 竖2横3
        { coords: [-3, -5], group: 23 },
        { coords: [-1, -5], group: 23 },
        { coords: [-1, -7], group: 23 },
        { coords: [-3, -7], group: 23 },
        // 竖2横4
        { coords: [-7, -9], group: 24 },
        { coords: [-5, -9], group: 24 },
        { coords: [-5, -11], group: 24 },
        { coords: [-7, -11], group: 24 },
        // 竖3横1
        { coords: [1, 15], group: 31 },
        { coords: [3, 15], group: 31 },
        { coords: [3, 13], group: 31 },
        { coords: [1, 13], group: 31 },
        // 竖3横2
        { coords: [5, 3], group: 32 },
        { coords: [7, 3], group: 32 },
        { coords: [7, 1], group: 32 },
        { coords: [5, 1], group: 32 },
        // 竖3横3
        { coords: [1, -1], group: 33 },
        { coords: [3, -1], group: 33 },
        { coords: [3, -3], group: 33 },
        { coords: [1, -3], group: 33 },
        // 竖3横4
        { coords: [5, -13], group: 34 },
        { coords: [7, -13], group: 34 },
        { coords: [7, -15], group: 34 },
        { coords: [5, -15], group: 34 },
        // 竖4横1
        { coords: [13, 11], group: 41 },
        { coords: [15, 11], group: 41 },
        { coords: [15, 9], group: 41 },
        { coords: [13, 9], group: 41 },
        // 竖4横2
        { coords: [9, 7], group: 42 },
        { coords: [11, 7], group: 42 },
        { coords: [11, 5], group: 42 },
        { coords: [9, 5], group: 42 },
        // 竖4横3
        { coords: [13, -5], group: 43 },
        { coords: [15, -5], group: 43 },
        { coords: [15, -7], group: 43 },
        { coords: [13, -7], group: 43 },
        // 竖4横4
        { coords: [9, -9], group: 44 },
        { coords: [11, -9], group: 44 },
        { coords: [11, -11], group: 44 },
        { coords: [9, -11], group: 44 },
    ],
};

// 绿色格子坐标映射
const greenCoordinates: ColorCoordinates = {
    level1: [
        { coords: [-12, 4], group: 12 }, // 竖1横2
        { coords: [-4, -4], group: 23 }, // 竖2横3
        { coords: [4, -12], group: 34 }, // 竖3横4
        { coords: [12, 12], group: 41 }, // 竖4横1
    ],
    level3: [
        // 竖1横1
        { coords: [-14, 10], group: 11 },
        // 竖1横2
        { coords: [-10, 6], group: 12 },
        // 竖1横3
        { coords: [-14, -6], group: 13 },
        // 竖1横4
        { coords: [-10, -10], group: 14 },
        // 竖2横1
        { coords: [-2, 14], group: 21 },
        // 竖2横2
        { coords: [-6, 2], group: 22 },
        // 竖2横3
        { coords: [-2, -2], group: 23 },
        // 竖2横4
        { coords: [-6, -14], group: 24 },
        // 竖3横1
        { coords: [2, 10], group: 31 },
        // 竖3横2
        { coords: [6, 6], group: 32 },
        // 竖3横3
        { coords: [2, -6], group: 33 },
        // 竖3横4
        { coords: [6, -10], group: 34 },
        // 竖4横1
        { coords: [14, 14], group: 41 },
        // 竖4横2
        { coords: [10, 2], group: 42 },
        // 竖4横3
        { coords: [14, -2], group: 43 },
        // 竖4横4
        { coords: [10, -14], group: 44 },
    ],
    level4: [
        // 竖1横1
        { coords: [-15, 11], group: 11 },
        { coords: [-13, 11], group: 11 },
        { coords: [-13, 9], group: 11 },
        { coords: [-15, 9], group: 11 },
        // 竖1横2
        { coords: [-11, 7], group: 12 },
        { coords: [-9, 7], group: 12 },
        { coords: [-9, 5], group: 12 },
        { coords: [-11, 5], group: 12 },
        // 竖1横3
        { coords: [-15, -5], group: 13 },
        { coords: [-13, -5], group: 13 },
        { coords: [-13, -7], group: 13 },
        { coords: [-15, -7], group: 13 },
        // 竖1横4
        { coords: [-11, -9], group: 14 },
        { coords: [-9, -9], group: 14 },
        { coords: [-9, -11], group: 14 },
        { coords: [-11, -11], group: 14 },
        // 竖2横1
        { coords: [-3, 15], group: 21 },
        { coords: [-1, 15], group: 21 },
        { coords: [-1, 13], group: 21 },
        { coords: [-3, 13], group: 21 },
        // 竖2横2
        { coords: [-7, 3], group: 22 },
        { coords: [-5, 3], group: 22 },
        { coords: [-5, 1], group: 22 },
        { coords: [-7, 1], group: 22 },
        // 竖2横3
        { coords: [-3, -1], group: 23 },
        { coords: [-1, -1], group: 23 },
        { coords: [-1, -3], group: 23 },
        { coords: [-3, -3], group: 23 },
        // 竖2横4
        { coords: [-7, -13], group: 24 },
        { coords: [-5, -13], group: 24 },
        { coords: [-5, -15], group: 24 },
        { coords: [-7, -15], group: 24 },
        // 竖3横1
        { coords: [1, 11], group: 31 },
        { coords: [3, 11], group: 31 },
        { coords: [3, 9], group: 31 },
        { coords: [1, 9], group: 31 },
        // 竖3横2
        { coords: [5, 7], group: 32 },
        { coords: [7, 7], group: 32 },
        { coords: [7, 5], group: 32 },
        { coords: [5, 5], group: 32 },
        // 竖3横3
        { coords: [1, -5], group: 33 },
        { coords: [3, -5], group: 33 },
        { coords: [3, -7], group: 33 },
        { coords: [1, -7], group: 33 },
        // 竖3横4
        { coords: [5, -9], group: 34 },
        { coords: [7, -9], group: 34 },
        { coords: [7, -11], group: 34 },
        { coords: [5, -11], group: 34 },
        // 竖4横1
        { coords: [13, 15], group: 41 },
        { coords: [15, 15], group: 41 },
        { coords: [15, 13], group: 41 },
        { coords: [13, 13], group: 41 },
        // 竖4横2
        { coords: [9, 3], group: 42 },
        { coords: [11, 3], group: 42 },
        { coords: [11, 1], group: 42 },
        { coords: [9, 1], group: 42 },
        // 竖4横3
        { coords: [13, -1], group: 43 },
        { coords: [15, -1], group: 43 },
        { coords: [15, -3], group: 43 },
        { coords: [13, -3], group: 43 },
        // 竖4横4
        { coords: [9, -13], group: 44 },
        { coords: [11, -13], group: 44 },
        { coords: [11, -15], group: 44 },
        { coords: [9, -15], group: 44 },
    ],
};

// 蓝色格子坐标映射
const blueCoordinates: ColorCoordinates = {
    level1: [
        { coords: [-12, -4], group: 13 }, // 竖1横3
        { coords: [-4, 4], group: 22 }, // 竖2横2
        { coords: [4, 12], group: 31 }, // 竖3横1
        { coords: [12, -12], group: 44 }, // 竖4横4
    ],
    level3: [
        // 竖1横1
        { coords: [-10, 10], group: 11 },
        // 竖1横2
        { coords: [-14, 6], group: 12 },
        // 竖1横3
        { coords: [-10, -6], group: 13 },
        // 竖1横4
        { coords: [-14, -10], group: 14 },
        // 竖2横1
        { coords: [-6, 14], group: 21 },
        // 竖2横2
        { coords: [-2, 2], group: 22 },
        // 竖2横3
        { coords: [-6, -2], group: 23 },
        // 竖2横4
        { coords: [-2, -14], group: 24 },
        // 竖3横1
        { coords: [6, 10], group: 31 },
        // 竖3横2
        { coords: [2, 6], group: 32 },
        // 竖3横3
        { coords: [6, -6], group: 33 },
        // 竖3横4
        { coords: [2, -10], group: 34 },
        // 竖4横1
        { coords: [10, 14], group: 41 },
        // 竖4横2
        { coords: [14, 2], group: 42 },
        // 竖4横3
        { coords: [10, -2], group: 43 },
        // 竖4横4
        { coords: [14, -14], group: 44 },
    ],
    level4: [
        // 竖1横1
        { coords: [-11, 11], group: 11 },
        { coords: [-9, 11], group: 11 },
        { coords: [-9, 9], group: 11 },
        { coords: [-11, 9], group: 11 },
        // 竖1横2
        { coords: [-15, 7], group: 12 },
        { coords: [-13, 7], group: 12 },
        { coords: [-13, 5], group: 12 },
        { coords: [-15, 5], group: 12 },
        // 竖1横3
        { coords: [-11, -5], group: 13 },
        { coords: [-9, -5], group: 13 },
        { coords: [-9, -7], group: 13 },
        { coords: [-11, -7], group: 13 },
        // 竖1横4
        { coords: [-15, -9], group: 14 },
        { coords: [-13, -9], group: 14 },
        { coords: [-13, -11], group: 14 },
        { coords: [-15, -11], group: 14 },
        // 竖2横1
        { coords: [-7, 15], group: 21 },
        { coords: [-5, 15], group: 21 },
        { coords: [-5, 13], group: 21 },
        { coords: [-7, 13], group: 21 },
        // 竖2横2
        { coords: [-3, 3], group: 22 },
        { coords: [-1, 3], group: 22 },
        { coords: [-1, 1], group: 22 },
        { coords: [-3, 1], group: 22 },
        // 竖2横3
        { coords: [-7, -1], group: 23 },
        { coords: [-5, -1], group: 23 },
        { coords: [-5, -3], group: 23 },
        { coords: [-7, -3], group: 23 },
        // 竖2横4
        { coords: [-3, -13], group: 24 },
        { coords: [-1, -13], group: 24 },
        { coords: [-1, -15], group: 24 },
        { coords: [-3, -15], group: 24 },
        // 竖3横1
        { coords: [5, 11], group: 31 },
        { coords: [7, 11], group: 31 },
        { coords: [7, 9], group: 31 },
        { coords: [5, 9], group: 31 },
        // 竖3横2
        { coords: [1, 7], group: 32 },
        { coords: [3, 7], group: 32 },
        { coords: [3, 5], group: 32 },
        { coords: [1, 5], group: 32 },
        // 竖3横3
        { coords: [5, -5], group: 33 },
        { coords: [7, -5], group: 33 },
        { coords: [7, -7], group: 33 },
        { coords: [5, -7], group: 33 },
        // 竖3横4
        { coords: [1, -9], group: 34 },
        { coords: [3, -9], group: 34 },
        { coords: [3, -11], group: 34 },
        { coords: [1, -11], group: 34 },
        // 竖4横1
        { coords: [9, 15], group: 41 },
        { coords: [11, 15], group: 41 },
        { coords: [11, 13], group: 41 },
        { coords: [9, 13], group: 41 },
        // 竖4横2
        { coords: [13, 3], group: 42 },
        { coords: [15, 3], group: 42 },
        { coords: [15, 1], group: 42 },
        { coords: [13, 1], group: 42 },
        // 竖4横3
        { coords: [9, -1], group: 43 },
        { coords: [11, -1], group: 43 },
        { coords: [11, -3], group: 43 },
        { coords: [9, -3], group: 43 },
        // 竖4横4
        { coords: [13, -13], group: 44 },
        { coords: [15, -13], group: 44 },
        { coords: [15, -15], group: 44 },
        { coords: [13, -15], group: 44 },
    ],
};

// 粉色格子坐标映射
const pinkCoordinates: ColorCoordinates = {
    level1: [
        { coords: [-12, -12], group: 14 }, // 竖1横4
        { coords: [-4, 12], group: 21 }, // 竖2横1
        { coords: [4, 4], group: 32 }, // 竖3横2
        { coords: [12, -4], group: 43 }, // 竖4横3
    ],
    level3: [
        // 竖1横1
        { coords: [-10, 14], group: 11 },
        // 竖1横2
        { coords: [-14, 2], group: 12 },
        // 竖1横3
        { coords: [-10, -2], group: 13 },
        // 竖1横4
        { coords: [-14, -14], group: 14 },
        // 竖2横1
        { coords: [-6, 10], group: 21 },
        // 竖2横2
        { coords: [-2, 6], group: 22 },
        // 竖2横3
        { coords: [-6, -6], group: 23 },
        // 竖2横4
        { coords: [-2, -10], group: 24 },
        // 竖3横1
        { coords: [6, 14], group: 31 },
        // 竖3横2
        { coords: [2, 2], group: 32 },
        // 竖3横3
        { coords: [6, -2], group: 33 },
        // 竖3横4
        { coords: [2, -14], group: 34 },
        // 竖4横1
        { coords: [10, 10], group: 41 },
        // 竖4横2
        { coords: [14, 6], group: 42 },
        // 竖4横3
        { coords: [10, -6], group: 43 },
        // 竖4横4
        { coords: [14, -10], group: 44 },
    ],
    level4: [
        // 竖1横1
        { coords: [-11, 15], group: 11 },
        { coords: [-9, 15], group: 11 },
        { coords: [-9, 13], group: 11 },
        { coords: [-11, 13], group: 11 },
        // 竖1横2
        { coords: [-15, 3], group: 12 },
        { coords: [-13, 3], group: 12 },
        { coords: [-13, 1], group: 12 },
        { coords: [-15, 1], group: 12 },
        // 竖1横3
        { coords: [-11, -1], group: 13 },
        { coords: [-9, -1], group: 13 },
        { coords: [-9, -3], group: 13 },
        { coords: [-11, -3], group: 13 },
        // 竖1横4
        { coords: [-15, -13], group: 14 },
        { coords: [-13, -13], group: 14 },
        { coords: [-13, -15], group: 14 },
        { coords: [-15, -15], group: 14 },
        // 竖2横1
        { coords: [-7, 11], group: 21 },
        { coords: [-5, 11], group: 21 },
        { coords: [-5, 9], group: 21 },
        { coords: [-7, 9], group: 21 },
        // 竖2横2
        { coords: [-3, 7], group: 22 },
        { coords: [-1, 7], group: 22 },
        { coords: [-1, 5], group: 22 },
        { coords: [-3, 5], group: 22 },
        // 竖2横3
        { coords: [-7, -5], group: 23 },
        { coords: [-5, -5], group: 23 },
        { coords: [-5, -7], group: 23 },
        { coords: [-7, -7], group: 23 },
        // 竖2横4
        { coords: [-3, -9], group: 24 },
        { coords: [-1, -9], group: 24 },
        { coords: [-1, -11], group: 24 },
        { coords: [-3, -11], group: 24 },
        // 竖3横1
        { coords: [5, 15], group: 31 },
        { coords: [7, 15], group: 31 },
        { coords: [7, 13], group: 31 },
        { coords: [5, 13], group: 31 },
        // 竖3横2
        { coords: [1, 3], group: 32 },
        { coords: [3, 3], group: 32 },
        { coords: [3, 1], group: 32 },
        { coords: [1, 1], group: 32 },
        // 竖3横3
        { coords: [5, -1], group: 33 },
        { coords: [7, -1], group: 33 },
        { coords: [7, -3], group: 33 },
        { coords: [5, -3], group: 33 },
        // 竖3横4
        { coords: [1, -13], group: 34 },
        { coords: [3, -13], group: 34 },
        { coords: [3, -15], group: 34 },
        { coords: [1, -15], group: 34 },
        // 竖4横1
        { coords: [9, 11], group: 41 },
        { coords: [11, 11], group: 41 },
        { coords: [11, 9], group: 41 },
        { coords: [9, 9], group: 41 },
        // 竖4横2
        { coords: [13, 7], group: 42 },
        { coords: [15, 7], group: 42 },
        { coords: [15, 5], group: 42 },
        { coords: [13, 5], group: 42 },
        // 竖4横3
        { coords: [9, -5], group: 43 },
        { coords: [11, -5], group: 43 },
        { coords: [11, -7], group: 43 },
        { coords: [9, -7], group: 43 },
        // 竖4横4
        { coords: [13, -9], group: 44 },
        { coords: [15, -9], group: 44 },
        { coords: [15, -11], group: 44 },
        { coords: [13, -11], group: 44 },
    ],
};

// 默认颜色坐标数据
export const DEFAULT_COLOR_COORDINATES: Record<BasicColorType, ColorCoordinates> = {
  red: redCoordinates,
  cyan: cyanCoordinates,
  yellow: yellowCoordinates,
  purple: purpleCoordinates,
  orange: orangeCoordinates,
  green: greenCoordinates,
  blue: blueCoordinates,
  pink: pinkCoordinates,
};

// 默认颜色值
export const DEFAULT_COLOR_VALUES: Record<BasicColorType, ColorValue> = {
  red: { name: '红色', hex: '#ef4444', rgb: [239, 68, 68], hsl: [0, 84, 60] },
  cyan: { name: '青色', hex: '#06b6d4', rgb: [6, 182, 212], hsl: [189, 94, 43] },
  yellow: { name: '黄色', hex: '#eab308', rgb: [234, 179, 8], hsl: [45, 93, 47] },
  purple: { name: '紫色', hex: '#a855f7', rgb: [168, 85, 247], hsl: [271, 91, 65] },
  orange: { name: '橙色', hex: '#f97316', rgb: [249, 115, 22], hsl: [25, 95, 53] },
  green: { name: '绿色', hex: '#22c55e', rgb: [34, 197, 94], hsl: [142, 71, 45] },
  blue: { name: '蓝色', hex: '#3b82f6', rgb: [59, 130, 246], hsl: [217, 91, 60] },
  pink: { name: '粉色', hex: '#ec4899', rgb: [236, 72, 153], hsl: [327, 82, 60] },
};

// Debug-3.1: 数据模型一致性修复 - 根据AVAILABLE_LEVELS自动生成DEFAULT_COLOR_VISIBILITY
const generateConsistentColorVisibility = (colorType: BasicColorType): ColorVisibility => {
  const availableLevels = AVAILABLE_LEVELS[colorType];
  const visibility: ColorVisibility = { showCells: true };
  
  // 为每个可用级别设置可见性属性
  availableLevels.forEach(level => {
    const levelKey = `showLevel${level}` as keyof ColorVisibility;
    (visibility as any)[levelKey] = true;
  });
  
  return visibility;
};

// 默认颜色可见性 - Debug-3.1修复: 100%基于AVAILABLE_LEVELS生成，确保数据一致性
export const DEFAULT_COLOR_VISIBILITY: Record<BasicColorType, ColorVisibility> = {
  red: generateConsistentColorVisibility('red'),
  cyan: generateConsistentColorVisibility('cyan'),
  yellow: generateConsistentColorVisibility('yellow'),
  purple: generateConsistentColorVisibility('purple'),
  orange: generateConsistentColorVisibility('orange'),
  green: generateConsistentColorVisibility('green'),
  blue: generateConsistentColorVisibility('blue'),
  pink: generateConsistentColorVisibility('pink'),
};

// 特殊坐标映射 (黑色格子)
export const SPECIAL_COORDINATES = new Map([
  ['0,0', 'A'],
  ['16,0', 'B'],
  ['-16,0', 'C'],
  ['0,16', 'D'],
  ['0,-16', 'E'],
  ['8,8', 'F'],
  ['-8,-8', 'G'],
  ['8,-8', 'H'],
  ['-8,8', 'I'],
  ['16,16', 'J'],
  ['-16,-16', 'K'],
  ['16,-16', 'L'],
  ['-16,16', 'M'],
]);

// 默认黑色格子数据
export const DEFAULT_BLACK_CELL_DATA: BlackCellData = {
  coordinates: Array.from(SPECIAL_COORDINATES.entries()).map(([coords, letter]) => {
    const [x, y] = coords.split(',').map(Number);
    return { coords: [x, y] as [number, number], letter };
  }),
  visibility: true,
};

// 网格尺寸常量（整合自gridStore.ts）
export const GRID_DIMENSIONS = {
  ROWS: 33,
  COLS: 33,
  TOTAL_CELLS: 1089,
  DEFAULT_CELL_SIZE: 24,
  MIN_CELL_SIZE: 12,
  MAX_CELL_SIZE: 48,
} as const;

// 网格中心坐标（整合自gridStore.ts）
export const GRID_CENTER = {
  X: 16,
  Y: 16,
} as const;

// 网格生成函数（整合自gridStore.ts）
const generateGridData = (): CellData[] => {
  const gridData: CellData[] = [];
  const centerX = GRID_CENTER.X;
  const centerY = GRID_CENTER.Y;
  
  for (let row = 0; row < GRID_DIMENSIONS.ROWS; row++) {
    for (let col = 0; col < GRID_DIMENSIONS.COLS; col++) {
      const x = col - centerX;
      const y = centerY - row;
      const cellId = row * GRID_DIMENSIONS.COLS + col;
      const number = cellId + 1;
      
      gridData.push({
        id: cellId,
        x,
        y,
        number,
        row,
        col,
        color: 'black', // 默认颜色
        level: 1,      // 默认级别
        group: null,   // 默认无分组
      });
    }
  }
  
  return gridData;
};

export interface BasicDataState {
  // 8种颜色坐标数据
  colorCoordinates: Record<BasicColorType, ColorCoordinates>;
  
  // 8种颜色值定义
  colorValues: Record<BasicColorType, ColorValue>;
  
  // 8种颜色可见性控制
  colorVisibility: Record<BasicColorType, ColorVisibility>;
  
  // 8种颜色级别规则
  colorLevelRules: Record<BasicColorType, number[]>;
  
  // 黑色格子数据
  blackCellData: BlackCellData;
  
  // 网格数据（整合自gridStore.ts）
  gridData: CellData[];
}

export interface BasicDataActions {
  // 基础数据操作
  setColorCoordinates: (colorType: BasicColorType, coordinates: ColorCoordinates) => void;
  updateColorValues: (colorType: BasicColorType, values: Partial<ColorValue>) => void;
  setColorVisibility: (colorType: BasicColorType, visibility: Partial<ColorVisibility>) => void;
  toggleColorLevel: (colorType: BasicColorType, level: 1 | 2 | 3 | 4) => void;
  setBlackCellData: (data: BlackCellData) => void;
  
  // 批量操作
  toggleAllColorCells: (show: boolean) => void;
  resetColorData: () => void;
  
  // 常量访问器
  getAvailableLevels: (colorType: BasicColorType) => number[];
  getColorValue: (colorType: BasicColorType) => ColorValue;
  getAllColorTypes: () => BasicColorType[];
  
  // 网格数据操作（整合自gridStore.ts）
  initializeGrid: () => void;
  updateGridCell: (cellId: number, updates: Partial<CellData>) => void;
  regenerateGrid: () => void;
  getGridDimensions: () => typeof GRID_DIMENSIONS;
  getGridCenter: () => typeof GRID_CENTER;
  getCellByCoordinate: (x: number, y: number) => CellData | undefined;
  getCellByPosition: (row: number, col: number) => CellData | undefined;
}

export type BasicDataStore = BasicDataState & BasicDataActions;

export const useBasicDataStore = create<BasicDataStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      colorCoordinates: DEFAULT_COLOR_COORDINATES,
      colorValues: DEFAULT_COLOR_VALUES,
      colorVisibility: DEFAULT_COLOR_VISIBILITY,
      colorLevelRules: AVAILABLE_LEVELS,
      blackCellData: DEFAULT_BLACK_CELL_DATA,
      gridData: [], // 网格数据初始状态

      // 基础数据操作
      setColorCoordinates: (colorType, coordinates) =>
        set((state) => ({
          colorCoordinates: {
            ...state.colorCoordinates,
            [colorType]: coordinates,
          },
        })),

      updateColorValues: (colorType, values) =>
        set((state) => ({
          colorValues: {
            ...state.colorValues,
            [colorType]: { ...state.colorValues[colorType], ...values },
          },
        })),

      setColorVisibility: (colorType, visibility) =>
        set((state) => ({
          colorVisibility: {
            ...state.colorVisibility,
            [colorType]: { ...state.colorVisibility[colorType], ...visibility },
          },
        })),

      toggleColorLevel: (colorType, level) =>
        set((state) => {
          const currentVisibility = state.colorVisibility[colorType];
          const levelKey = `showLevel${level}` as keyof ColorVisibility;

          // 🔧 修复：检查级别是否存在于AVAILABLE_LEVELS中
          const availableLevels = AVAILABLE_LEVELS[colorType];
          if (!availableLevels.includes(level)) {
            console.warn(`⚠️ 尝试切换不存在的级别: ${colorType}-${level}`);
            return state;
          }

          if (levelKey in currentVisibility) {
            const oldValue = currentVisibility[levelKey];
            const newValue = !oldValue;

            console.log(`🔄 toggleColorLevel: ${colorType}-${level}`, {
              oldValue,
              newValue,
              levelKey,
              currentVisibility
            });

            const newState = {
              colorVisibility: {
                ...state.colorVisibility,
                [colorType]: {
                  ...currentVisibility,
                  [levelKey]: newValue,
                },
              },
            };

            console.log(`✅ toggleColorLevel完成: ${colorType}-${level}`, {
              newVisibility: newState.colorVisibility[colorType]
            });

            return newState;
          }

          console.warn(`⚠️ 级别键不存在于可见性对象中: ${levelKey}`, currentVisibility);
          return state;
        }),

      setBlackCellData: (data) =>
        set(() => ({ blackCellData: data })),

      // 批量操作
      toggleAllColorCells: (show) =>
        set((state) => {
          const newVisibility: Record<BasicColorType, ColorVisibility> = {} as any;
          Object.keys(state.colorVisibility).forEach((colorType) => {
            newVisibility[colorType as BasicColorType] = {
              ...state.colorVisibility[colorType as BasicColorType],
              showCells: show,
            };
          });
          return { colorVisibility: newVisibility };
        }),

      resetColorData: () =>
        set(() => ({
          colorCoordinates: DEFAULT_COLOR_COORDINATES,
          colorValues: DEFAULT_COLOR_VALUES,
          colorVisibility: DEFAULT_COLOR_VISIBILITY,
          colorLevelRules: AVAILABLE_LEVELS,
          blackCellData: DEFAULT_BLACK_CELL_DATA,
        })),

      // 常量访问器
      getAvailableLevels: (colorType) => AVAILABLE_LEVELS[colorType],
      getColorValue: (colorType) => get().colorValues[colorType],
      getAllColorTypes: () => Object.keys(DEFAULT_COLOR_COORDINATES) as BasicColorType[],

      // 网格数据操作（整合自gridStore.ts）
      initializeGrid: () => {
        const gridData = generateGridData();
        set(() => ({ gridData }));
      },

      updateGridCell: (cellId, updates) =>
        set((state) => ({
          gridData: state.gridData.map((cell) =>
            cell.id === cellId ? { ...cell, ...updates } : cell
          ),
        })),

      regenerateGrid: () => {
        const gridData = generateGridData();
        set(() => ({ gridData }));
      },

      // 网格常量访问器
      getGridDimensions: () => GRID_DIMENSIONS,
      getGridCenter: () => GRID_CENTER,
      
      getCellByCoordinate: (x, y) => {
        const state = get();
        return state.gridData.find((cell) => cell.x === x && cell.y === y);
      },

      getCellByPosition: (row, col) => {
        const state = get();
        return state.gridData.find((cell) => cell.row === row && cell.col === col);
      },
    }),
    {
      name: 'basic-data-store',
      version: 2, // 版本升级，包含网格数据
    }
  )
);

// 选择器函数
export const useColorCoordinates = (colorType?: BasicColorType) => 
  useBasicDataStore((state) => 
    colorType ? state.colorCoordinates[colorType] : state.colorCoordinates
  );

export const useColorValues = (colorType?: BasicColorType) => 
  useBasicDataStore((state) => 
    colorType ? state.colorValues[colorType] : state.colorValues
  );

export const useColorVisibility = (colorType?: BasicColorType) => 
  useBasicDataStore((state) => 
    colorType ? state.colorVisibility[colorType] : state.colorVisibility
  );

export const useBlackCellData = () => 
  useBasicDataStore((state) => state.blackCellData);

export const getAllColorTypes = (): BasicColorType[] => [
  'red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'
];

// 网格相关选择器（整合自gridStore.ts）
export const useGridData = () => useBasicDataStore((state) => state.gridData); 