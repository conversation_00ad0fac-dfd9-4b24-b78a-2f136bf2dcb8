/**
 * 组合数据Store - 管理组合模式数据
 * 🎯 核心职责：默认模式、撇捺分组、横竖分组、组合配置等常量
 * 📦 原Constants: constants/business/combinations.ts, constants/business/groups.ts, constants/business/modes.ts
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { BasicColorType } from './basicDataStore';

// ==================== 常量定义 ====================

// 分组类型常量
export const GROUP_TYPES = {
  STANDARD: 'standard',
  GRID: 'grid',
} as const;

// 模式类型常量
export const MODE_TYPES = {
  DEFAULT: 'default',
  PIE_NA: 'pieNa',
  ZHU_HENG: 'zhuHeng', 
  MIXED: 'mixed',
} as const;

// 标准分组和网格分组
export const STANDARD_GROUPS = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10] as const;
export const GRID_GROUPS = [11, 12, 13, 14, 21, 22, 23, 24, 31, 32, 33, 34, 41, 42, 43, 44] as const;

// 组合规则常量
export const COMBINATION_RULES = {
  // 撇捺分组规则（红青黄紫）
  PIE_NA_RULES: {
    colors: ['red', 'cyan', 'yellow', 'purple'] as const,
    allowedGroups: STANDARD_GROUPS,
    defaultActiveGroups: [1, 2, 3, 4, 5],
    maxActiveGroups: 8,
    swapPairs: [
      [1, 2], [3, 4], [5, 6], [7, 8], [9, 10]
    ] as const,
  },
  
  // 横竖分组规则（橙绿蓝粉）
  ZHU_HENG_RULES: {
    colors: ['orange', 'green', 'blue', 'pink'] as const,
    allowedGroups: GRID_GROUPS,
    defaultActiveGroups: [11, 12, 21, 22],
    maxActiveGroups: 12,
    swapPairs: [
      [11, 12], [13, 14], [21, 22], [23, 24], 
      [31, 32], [33, 34], [41, 42], [43, 44]
    ] as const,
  },
  
  // 混合模式规则
  MIXED_RULES: {
    colors: ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'] as const,
    allowedGroups: [...STANDARD_GROUPS, ...GRID_GROUPS],
    maxActiveGroups: 16,
    restrictionMatrix: {
      // 定义哪些颜色组合可以同时激活
      red: ['cyan', 'yellow', 'purple'],
      cyan: ['red', 'yellow', 'purple'],
      yellow: ['red', 'cyan', 'purple'],
      purple: ['red', 'cyan', 'yellow'],
      orange: ['green', 'blue', 'pink'],
      green: ['orange', 'blue', 'pink'],
      blue: ['orange', 'green', 'pink'],
      pink: ['orange', 'green', 'blue'],
    },
  },
} as const;

// 模式切换配置常量
export const MODE_SWITCH_CONFIG = {
  // 模式切换动画时长
  TRANSITION_DURATION: 300,
  
  // 模式描述
  MODE_DESCRIPTIONS: {
    [MODE_TYPES.DEFAULT]: '显示所有颜色和等级',
    [MODE_TYPES.PIE_NA]: '撇捺分组模式（红青黄紫）',
    [MODE_TYPES.ZHU_HENG]: '横竖分组模式（橙绿蓝粉）',
    [MODE_TYPES.MIXED]: '混合模式（自由组合）',
  },
  
  // 模式图标
  MODE_ICONS: {
    [MODE_TYPES.DEFAULT]: '🎨',
    [MODE_TYPES.PIE_NA]: '⚡',
    [MODE_TYPES.ZHU_HENG]: '📐',
    [MODE_TYPES.MIXED]: '🔀',
  },
  
  // 模式键盘快捷键
  MODE_SHORTCUTS: {
    [MODE_TYPES.DEFAULT]: 'D',
    [MODE_TYPES.PIE_NA]: 'P',
    [MODE_TYPES.ZHU_HENG]: 'Z',
    [MODE_TYPES.MIXED]: 'M',
  },
} as const;

// 分组操作限制常量
export const GROUP_OPERATION_LIMITS = {
  MIN_ACTIVE_GROUPS: 1,
  MAX_ACTIVE_GROUPS_PER_COLOR: 4,
  MAX_TOTAL_ACTIVE_GROUPS: 16,
  SWAP_OPERATION_COOLDOWN: 500, // ms
  BULK_OPERATION_MAX_SIZE: 10,
} as const;

// 组合数据相关类型定义
export interface SwapGroupsData {
  group1: number;
  group2: number;
}

export interface PieNaGroupConfig {
  colors: ['red', 'cyan', 'yellow', 'purple'];
  displayName: string;
  defaultGroups: readonly number[];
}

export interface ZhuHengGroupConfig {
  colors: ['orange', 'green', 'blue', 'pink'];
  displayName: string;
  defaultGroups: readonly number[];
}

export interface CombinationDataState {
  // 默认模式配置
  defaultModeConfig: {
    showAllColors: boolean;
    showAllLevels: boolean;
  };
  
  // 撇捺分组配置 (红青黄紫)
  pieNaGroupConfig: PieNaGroupConfig;
  
  // 横竖分组配置 (橙绿蓝粉)
  zhuHengGroupConfig: ZhuHengGroupConfig;
  
  // 当前模式设置
  currentMode: keyof typeof MODE_TYPES;
  
  // 分组选择状态
  selectedGroups: Record<BasicColorType, Set<number>>;
  
  // 交换分组数据
  swapGroupsData: Record<BasicColorType, SwapGroupsData>;
  
  // 模式激活状态
  modeActivation: Record<BasicColorType, boolean>;
  
  // 操作历史记录
  operationHistory: Array<{
    timestamp: number;
    operation: string;
    data: any;
  }>;
  
  // 快捷键状态
  shortcutEnabled: boolean;
}

export interface CombinationDataActions {
  // 基础分组操作
  toggleGroup: (colorType: BasicColorType, group: number) => void;
  setSelectedGroups: (colorType: BasicColorType, groups: Set<number>) => void;
  clearSelectedGroups: (colorType: BasicColorType) => void;
  
  // 交换分组操作
  setSwapGroups: (colorType: BasicColorType, group1: number, group2: number) => void;
  executeSwapGroups: (colorType: BasicColorType) => void;
  
  // 模式切换操作
  setCurrentMode: (mode: keyof typeof MODE_TYPES) => void;
  toggleModeActivation: (colorType: BasicColorType) => void;
  
  // 批量分组操作
  togglePieNaGroup: (groupNum: number) => void;
  toggleZhuHengGroup: (groupNum: number) => void;
  resetAllGroups: () => void;
  
  // 配置更新
  updateDefaultModeConfig: (config: Partial<{ showAllColors: boolean; showAllLevels: boolean }>) => void;
  
  // 常量访问方法
  getAvailableGroups: (mode?: keyof typeof MODE_TYPES) => readonly number[];
  getModeDescription: (mode: keyof typeof MODE_TYPES) => string;
  getModeIcon: (mode: keyof typeof MODE_TYPES) => string;
  getSwapPairs: (mode: keyof typeof MODE_TYPES) => readonly (readonly [number, number])[];
  validateGroupSelection: (colorType: BasicColorType, groups: number[]) => boolean;
  
  // 操作历史
  addOperationHistory: (operation: string, data: any) => void;
  clearOperationHistory: () => void;
  
  // 快捷键
  toggleShortcuts: () => void;
}

export type CombinationDataStore = CombinationDataState & CombinationDataActions;

// ==================== 默认配置 ====================

const DEFAULT_PIE_NA_CONFIG: PieNaGroupConfig = {
  colors: ['red', 'cyan', 'yellow', 'purple'],
  displayName: '撇捺分组',
  defaultGroups: [...STANDARD_GROUPS],
};

const DEFAULT_ZHU_HENG_CONFIG: ZhuHengGroupConfig = {
  colors: ['orange', 'green', 'blue', 'pink'],
  displayName: '横竖分组',
  defaultGroups: [...GRID_GROUPS],
};

const DEFAULT_SELECTED_GROUPS: Record<BasicColorType, Set<number>> = {
  red: new Set(),
  orange: new Set(),
  yellow: new Set(),
  green: new Set(),
  cyan: new Set(),
  blue: new Set(),
  purple: new Set(),
  pink: new Set(),
};

const DEFAULT_SWAP_GROUPS: Record<BasicColorType, SwapGroupsData> = {
  red: { group1: 1, group2: 2 },
  orange: { group1: 11, group2: 12 },
  yellow: { group1: 3, group2: 4 },
  green: { group1: 13, group2: 14 },
  cyan: { group1: 5, group2: 6 },
  blue: { group1: 21, group2: 22 },
  purple: { group1: 7, group2: 8 },
  pink: { group1: 23, group2: 24 },
};

const DEFAULT_MODE_ACTIVATION: Record<BasicColorType, boolean> = {
  red: true,
  orange: true,
  yellow: true,
  green: true,
  cyan: true,
  blue: true,
  purple: true,
  pink: true,
};

// ==================== Store 创建 ====================

export const useCombinationDataStore = create<CombinationDataStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      defaultModeConfig: {
        showAllColors: true,
        showAllLevels: true,
      },
      pieNaGroupConfig: DEFAULT_PIE_NA_CONFIG,
      zhuHengGroupConfig: DEFAULT_ZHU_HENG_CONFIG,
      currentMode: 'DEFAULT',
      selectedGroups: DEFAULT_SELECTED_GROUPS,
      swapGroupsData: DEFAULT_SWAP_GROUPS,
      modeActivation: DEFAULT_MODE_ACTIVATION,
      operationHistory: [],
      shortcutEnabled: true,
      
      // 基础分组操作
      toggleGroup: (colorType, group) => {
        set((state) => {
          const currentGroups = state.selectedGroups[colorType];
          const newGroups = new Set(currentGroups);
          
          if (newGroups.has(group)) {
            newGroups.delete(group);
          } else {
            newGroups.add(group);
          }
          
          return {
            selectedGroups: {
              ...state.selectedGroups,
              [colorType]: newGroups,
            },
          };
        });
        
        // 记录操作历史
        get().addOperationHistory('toggleGroup', { colorType, group });
      },
      
      setSelectedGroups: (colorType, groups) => {
        set((state) => ({
          selectedGroups: {
            ...state.selectedGroups,
            [colorType]: new Set(groups),
          },
        }));
      },
      
      clearSelectedGroups: (colorType) => {
        set((state) => ({
          selectedGroups: {
            ...state.selectedGroups,
            [colorType]: new Set(),
          },
        }));
      },
      
      // 交换分组操作
      setSwapGroups: (colorType, group1, group2) => {
        set((state) => ({
          swapGroupsData: {
            ...state.swapGroupsData,
            [colorType]: { group1, group2 },
          },
        }));
      },
      
      executeSwapGroups: (colorType) => {
        const state = get();
        const { group1, group2 } = state.swapGroupsData[colorType];
        
        // 执行交换逻辑
        const currentGroups = state.selectedGroups[colorType];
        const newGroups = new Set(currentGroups);
        
        const hasGroup1 = newGroups.has(group1);
        const hasGroup2 = newGroups.has(group2);
        
        if (hasGroup1 && !hasGroup2) {
          newGroups.delete(group1);
          newGroups.add(group2);
        } else if (!hasGroup1 && hasGroup2) {
          newGroups.delete(group2);
          newGroups.add(group1);
        }
        
        set((state) => ({
          selectedGroups: {
            ...state.selectedGroups,
            [colorType]: newGroups,
          },
        }));
        
        // 记录操作历史
        get().addOperationHistory('executeSwapGroups', { colorType, group1, group2 });
      },
      
      // 模式切换操作
      setCurrentMode: (mode) => {
        set({ currentMode: mode });
        get().addOperationHistory('setCurrentMode', { mode });
      },
      
      toggleModeActivation: (colorType) => {
        set((state) => ({
          modeActivation: {
            ...state.modeActivation,
            [colorType]: !state.modeActivation[colorType],
          },
        }));
      },
      
      // 批量分组操作
      togglePieNaGroup: (groupNum) => {
        const pieNaColors = COMBINATION_RULES.PIE_NA_RULES.colors;
        pieNaColors.forEach(color => {
          get().toggleGroup(color, groupNum);
        });
      },
      
      toggleZhuHengGroup: (groupNum) => {
        const zhuHengColors = COMBINATION_RULES.ZHU_HENG_RULES.colors;
        zhuHengColors.forEach(color => {
          get().toggleGroup(color, groupNum);
        });
      },
      
      resetAllGroups: () => {
        set({ selectedGroups: DEFAULT_SELECTED_GROUPS });
        get().addOperationHistory('resetAllGroups', {});
      },
      
      // 配置更新
      updateDefaultModeConfig: (config) => {
        set((state) => ({
          defaultModeConfig: {
            ...state.defaultModeConfig,
            ...config,
          },
        }));
      },
      
      // 常量访问方法
      getAvailableGroups: (mode) => {
        const currentMode = mode || get().currentMode;
        switch (currentMode) {
          case 'PIE_NA':
            return COMBINATION_RULES.PIE_NA_RULES.allowedGroups;
          case 'ZHU_HENG':
            return COMBINATION_RULES.ZHU_HENG_RULES.allowedGroups;
          case 'MIXED':
            return COMBINATION_RULES.MIXED_RULES.allowedGroups;
          default:
            return [...STANDARD_GROUPS, ...GRID_GROUPS];
        }
      },
      
      getModeDescription: (mode) => {
        // 将大写键转换为对应的小写值
        const modeKey = mode as keyof typeof MODE_TYPES;
        const modeValue = MODE_TYPES[modeKey];
        return MODE_SWITCH_CONFIG.MODE_DESCRIPTIONS[modeValue] || '';
      },
      
      getModeIcon: (mode) => {
        // 将大写键转换为对应的小写值
        const modeKey = mode as keyof typeof MODE_TYPES;
        const modeValue = MODE_TYPES[modeKey];
        return MODE_SWITCH_CONFIG.MODE_ICONS[modeValue] || '❓';
      },
      
      getSwapPairs: (mode) => {
        const currentMode = mode || get().currentMode;
        switch (currentMode) {
          case 'PIE_NA':
            return COMBINATION_RULES.PIE_NA_RULES.swapPairs;
          case 'ZHU_HENG':
            return COMBINATION_RULES.ZHU_HENG_RULES.swapPairs;
          default:
            return [];
        }
      },
      
      validateGroupSelection: (colorType, groups) => {
        const currentMode = get().currentMode;
        const maxGroups = GROUP_OPERATION_LIMITS.MAX_ACTIVE_GROUPS_PER_COLOR;
        
        if (groups.length > maxGroups) {
          return false;
        }
        
        const availableGroups = get().getAvailableGroups(currentMode);
        return groups.every(group => availableGroups.includes(group));
      },
      
      // 操作历史
      addOperationHistory: (operation, data) => {
        set((state) => ({
          operationHistory: [
            ...state.operationHistory.slice(-19), // 保留最近20条记录
            {
              timestamp: Date.now(),
              operation,
              data,
            },
          ],
        }));
      },
      
      clearOperationHistory: () => {
        set({ operationHistory: [] });
      },
      
      // 快捷键
      toggleShortcuts: () => {
        set((state) => ({ shortcutEnabled: !state.shortcutEnabled }));
      },
    }),
    {
      name: 'combination-data-storage',
      version: 1,
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          
          try {
            const data = JSON.parse(str);
            
            // 修复 selectedGroups 中的 Set 序列化问题
            if (data.state && data.state.selectedGroups) {
              const fixedSelectedGroups: Record<BasicColorType, Set<number>> = {} as Record<BasicColorType, Set<number>>;
              Object.entries(data.state.selectedGroups).forEach(([color, groups]) => {
                // 处理多种可能的格式：Array、Object、Set
                if (Array.isArray(groups)) {
                  fixedSelectedGroups[color as BasicColorType] = new Set(groups as number[]);
                } else if (groups && typeof groups === 'object' && 'values' in groups) {
                  // 可能是序列化的 Set 对象
                  fixedSelectedGroups[color as BasicColorType] = new Set(Object.values(groups as any) as number[]);
                } else if (groups instanceof Set) {
                  fixedSelectedGroups[color as BasicColorType] = groups;
                } else {
                  // 默认创建空 Set
                  fixedSelectedGroups[color as BasicColorType] = new Set<number>();
                }
              });
              data.state.selectedGroups = fixedSelectedGroups;
            }
            
            return data;
          } catch (error) {
            console.warn('解析 combination-data-storage 时出错:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            // 将 selectedGroups 中的 Set 转换为 Array 进行序列化
            const valueToStore = {
              ...value,
              state: {
                ...value.state,
                selectedGroups: Object.fromEntries(
                  Object.entries(value.state.selectedGroups).map(([color, groups]) => [
                    color,
                    Array.from(groups as Set<number>)
                  ])
                )
              }
            };
            
            localStorage.setItem(name, JSON.stringify(valueToStore));
          } catch (error) {
            console.warn('序列化 combination-data-storage 时出错:', error);
          }
        },
        removeItem: (name) => localStorage.removeItem(name),
      },
    }
  )
);

// ==================== 选择器 Hooks ====================

export const useSelectedGroups = (colorType?: BasicColorType) => 
  useCombinationDataStore((state) => 
    colorType ? state.selectedGroups[colorType] : state.selectedGroups
  );

export const useSwapGroups = (colorType?: BasicColorType) => 
  useCombinationDataStore((state) => 
    colorType ? state.swapGroupsData[colorType] : state.swapGroupsData
  );

export const useCurrentMode = () => 
  useCombinationDataStore((state) => state.currentMode);

export const useModeActivation = (colorType?: BasicColorType) => 
  useCombinationDataStore((state) => 
    colorType ? state.modeActivation[colorType] : state.modeActivation
  );

export const useOperationHistory = () => 
  useCombinationDataStore((state) => state.operationHistory);

export const useModeConstants = () => ({
  MODE_TYPES,
  GROUP_TYPES,
  COMBINATION_RULES,
  MODE_SWITCH_CONFIG,
  GROUP_OPERATION_LIMITS,
});

// 注意：常量已经在上面 export 了，这里不需要重复导出 