/**
 * 业务数据Store - 管理业务逻辑数据
 * 🎯 核心职责：点击状态、分组模式、混合模式、交互状态、UI状态、版本管理等
 * 📦 对应Constants: constants/business-data/
 * 🔄 整合内容：原uiStore.ts的UI状态管理，原versionStore.ts的版本管理功能
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 业务数据相关类型定义
export type BusinessMode = 'default' | 'group' | 'mixed';

// UI相关类型定义（整合自uiStore.ts）
export type PanelType = 
  | 'black'
  | 'red'
  | 'orange'
  | 'cyan'
  | 'yellow'
  | 'purple'
  | 'green'
  | 'blue'
  | 'pink'
  | 'settings'
  | 'r2-style';

export type SubPageType = 'main' | 'groupMode' | 'mixedMode';
export type ToastType = 'success' | 'error' | 'info' | 'warning';

// 版本管理相关类型定义（整合自versionStore.ts）
export type VersionType = 'default' | 'groupMode' | 'mixedMode' | 'matrix';

export interface VersionData {
  name: string;
  data: any;
  timestamp: number;
  description?: string;
}

export interface InteractionState {
  hoverInfo: {
    x: number;
    y: number;
    content: string;
  } | null;
  isDragging: boolean;
  selectedCells: Set<string>;
}

export interface ToastState {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  show: boolean;
  duration: number;
}

export interface BusinessDataState {
  // 业务状态数据
  clickedCells: Set<string>;
  currentActiveMode: BusinessMode;
  interactionState: InteractionState;
  
  // 版本管理数据（整合自versionStore.ts）
  defaultVersions: Record<string, VersionData>;
  currentDefaultVersion: string;
  newDefaultVersionName: string;
  groupModeVersions: Record<string, VersionData>;
  currentGroupModeVersion: string;
  newGroupModeVersionName: string;
  mixedModeVersions: Record<string, VersionData>;
  currentMixedModeVersion: string;
  newMixedModeVersionName: string;
  matrixModeVersions: Record<string, VersionData>;
  currentMatrixModeVersion: string;
  newMatrixModeVersionName: string;
  
  // UI状态数据（整合自uiStore.ts）
  activePanel: PanelType;
  settingsSubPage: SubPageType;
  singleColorMode: string | null;
  showSpecificGroup: number | null;
  isClient: boolean;
  
  // 提示状态
  toastState: ToastState;
}

export interface BusinessDataActions {
  // 业务数据操作
  handleCellClick: (cellKey: string) => void;
  clearAllClickedCells: () => void;
  setCellsSelection: (cellKeys: string[], selected: boolean) => void;
  
  // 版本管理操作（整合自versionStore.ts）
  saveVersion: (type: VersionType, name: string, data: any) => void;
  switchToVersion: (type: VersionType, name: string) => void;
  deleteVersion: (type: VersionType, name: string) => void;
  renameVersion: (type: VersionType, oldName: string, newName: string) => void;
  setCurrentVersion: (type: VersionType, name: string) => void;
  getCurrentVersionData: (type: VersionType) => VersionData | null;
  setNewVersionName: (type: VersionType, name: string) => void;
  clearNewVersionName: (type: VersionType) => void;
  getVersionList: (type: VersionType) => string[];
  getVersionCount: (type: VersionType) => number;
  hasVersion: (type: VersionType, name: string) => boolean;
  exportVersions: (type: VersionType) => string;
  importVersions: (type: VersionType, data: string) => boolean;
  clearAllVersions: (type: VersionType) => void;
  resetVersionState: () => void;
  syncBetweenVersions: (fromType: VersionType, toType: VersionType, versionName?: string) => void;
  
  // UI状态操作（整合自uiStore.ts）
  setActivePanel: (panel: PanelType) => void;
  setSettingsSubPage: (subPage: SubPageType) => void;
  setSingleColorMode: (color: string | null) => void;
  exitSingleColorMode: () => void;
  setShowSpecificGroup: (group: number | null) => void;
  setIsClient: (isClient: boolean) => void;
  resetUIState: () => void;
  
  // 模式切换
  switchMode: (mode: BusinessMode) => void;
  
  // 交互状态操作
  setHoverInfo: (info: InteractionState['hoverInfo']) => void;
  setDragging: (isDragging: boolean) => void;
  
  // 提示操作
  showToast: (message: string, type: ToastState['type'], duration?: number) => void;
  showToastNotification: (message: string, type?: ToastType) => void;
  hideToast: () => void;
  
  // 重置操作
  resetBusinessData: () => void;
}

// 默认交互状态
const DEFAULT_INTERACTION_STATE: InteractionState = {
  hoverInfo: null,
  isDragging: false,
  selectedCells: new Set<string>()
};

// 默认提示状态
const DEFAULT_TOAST_STATE: ToastState = {
  message: '',
  type: 'info',
  show: false,
  duration: 3000
};

// 默认版本状态（整合自versionStore.ts）
const DEFAULT_VERSION_STATE = {
  defaultVersions: {
    '默认版本': {
      name: '默认版本',
      timestamp: Date.now(),
      description: '系统默认配置',
      data: {},
    },
  },
  currentDefaultVersion: '默认版本',
  newDefaultVersionName: '',
  groupModeVersions: {
    '默认分组': {
      name: '默认分组',
      timestamp: Date.now(),
      description: '默认分组模式配置',
      data: {},
    },
  },
  currentGroupModeVersion: '默认分组',
  newGroupModeVersionName: '',
  mixedModeVersions: {
    '默认混合': {
      name: '默认混合',
      timestamp: Date.now(),
      description: '默认混合模式配置',
      data: {},
    },
  },
  currentMixedModeVersion: '默认混合',
  newMixedModeVersionName: '',
  matrixModeVersions: {
    '默认矩阵': {
      name: '默认矩阵',
      timestamp: Date.now(),
      description: '默认矩阵模式配置',
      data: {},
    },
  },
  currentMatrixModeVersion: '默认矩阵',
  newMatrixModeVersionName: '',
};

// 默认UI状态（整合自uiStore.ts）
const DEFAULT_UI_STATE = {
  activePanel: 'settings' as PanelType,
  settingsSubPage: 'main' as SubPageType,
  singleColorMode: null,
  showSpecificGroup: null,
  isClient: false,
};

export const useBusinessDataStore = create<BusinessDataState & BusinessDataActions>()(
  persist(
    (set, get) => ({
      // 初始状态
      clickedCells: new Set<string>(),
      currentActiveMode: 'default',
      interactionState: DEFAULT_INTERACTION_STATE,
      toastState: DEFAULT_TOAST_STATE,
      
      // 版本管理初始状态（整合自versionStore.ts）
      ...DEFAULT_VERSION_STATE,
      
      // UI状态初始状态（整合自uiStore.ts）
      ...DEFAULT_UI_STATE,

      // 业务数据操作
      handleCellClick: (cellKey) =>
        set((state) => {
          const newClickedCells = new Set(state.clickedCells);
          if (newClickedCells.has(cellKey)) {
            newClickedCells.delete(cellKey);
          } else {
            newClickedCells.add(cellKey);
          }
          return { clickedCells: newClickedCells };
        }),

      clearAllClickedCells: () =>
        set(() => ({ clickedCells: new Set<string>() })),

      setCellsSelection: (cellKeys, selected) =>
        set((state) => {
          const newClickedCells = new Set(state.clickedCells);
          cellKeys.forEach(key => {
            if (selected) {
              newClickedCells.add(key);
            } else {
              newClickedCells.delete(key);
            }
          });
          return { clickedCells: newClickedCells };
        }),

      // 版本管理操作（整合自versionStore.ts）
      saveVersion: (type: VersionType, name: string, data: any) => {
        const timestamp = Date.now();
        const versionData: VersionData = {
          name,
          timestamp,
          description: `保存于 ${new Date(timestamp).toLocaleString()}`,
          data,
        };
        
        set((state) => {
          switch (type) {
            case 'default':
              return {
                defaultVersions: {
                  ...state.defaultVersions,
                  [name]: versionData,
                },
              };
            case 'groupMode':
              return {
                groupModeVersions: {
                  ...state.groupModeVersions,
                  [name]: versionData,
                },
              };
            case 'mixedMode':
              return {
                mixedModeVersions: {
                  ...state.mixedModeVersions,
                  [name]: versionData,
                },
              };
            case 'matrix':
              return {
                matrixModeVersions: {
                  ...state.matrixModeVersions,
                  [name]: versionData,
                },
              };
            default:
              return state;
          }
        });
      },

      switchToVersion: (type: VersionType, name: string) => {
        set((state) => {
          switch (type) {
            case 'default':
              return { currentDefaultVersion: name };
            case 'groupMode':
              return { currentGroupModeVersion: name };
            case 'mixedMode':
              return { currentMixedModeVersion: name };
            case 'matrix':
              return { currentMatrixModeVersion: name };
            default:
              return state;
          }
        });
      },

      deleteVersion: (type: VersionType, name: string) => {
        set((state) => {
          switch (type) {
            case 'default': {
              const newVersions = { ...state.defaultVersions };
              delete newVersions[name];
              return {
                defaultVersions: newVersions,
                currentDefaultVersion: state.currentDefaultVersion === name 
                  ? Object.keys(newVersions)[0] || '默认版本'
                  : state.currentDefaultVersion,
              };
            }
            case 'groupMode': {
              const newVersions = { ...state.groupModeVersions };
              delete newVersions[name];
              return {
                groupModeVersions: newVersions,
                currentGroupModeVersion: state.currentGroupModeVersion === name 
                  ? Object.keys(newVersions)[0] || '默认分组'
                  : state.currentGroupModeVersion,
              };
            }
            case 'mixedMode': {
              const newVersions = { ...state.mixedModeVersions };
              delete newVersions[name];
              return {
                mixedModeVersions: newVersions,
                currentMixedModeVersion: state.currentMixedModeVersion === name 
                  ? Object.keys(newVersions)[0] || '默认混合'
                  : state.currentMixedModeVersion,
              };
            }
            case 'matrix': {
              const newVersions = { ...state.matrixModeVersions };
              delete newVersions[name];
              return {
                matrixModeVersions: newVersions,
                currentMatrixModeVersion: state.currentMatrixModeVersion === name 
                  ? Object.keys(newVersions)[0] || '默认矩阵'
                  : state.currentMatrixModeVersion,
              };
            }
            default:
              return state;
          }
        });
      },

      renameVersion: (type: VersionType, oldName: string, newName: string) => {
        set((state) => {
          switch (type) {
            case 'default': {
              const oldVersion = state.defaultVersions[oldName];
              if (!oldVersion) return state;
              
              const newVersions = { ...state.defaultVersions };
              delete newVersions[oldName];
              newVersions[newName] = { ...oldVersion, name: newName };
              
              return {
                defaultVersions: newVersions,
                currentDefaultVersion: state.currentDefaultVersion === oldName 
                  ? newName 
                  : state.currentDefaultVersion,
              };
            }
            case 'groupMode': {
              const oldVersion = state.groupModeVersions[oldName];
              if (!oldVersion) return state;
              
              const newVersions = { ...state.groupModeVersions };
              delete newVersions[oldName];
              newVersions[newName] = { ...oldVersion, name: newName };
              
              return {
                groupModeVersions: newVersions,
                currentGroupModeVersion: state.currentGroupModeVersion === oldName 
                  ? newName 
                  : state.currentGroupModeVersion,
              };
            }
            case 'mixedMode': {
              const oldVersion = state.mixedModeVersions[oldName];
              if (!oldVersion) return state;
              
              const newVersions = { ...state.mixedModeVersions };
              delete newVersions[oldName];
              newVersions[newName] = { ...oldVersion, name: newName };
              
              return {
                mixedModeVersions: newVersions,
                currentMixedModeVersion: state.currentMixedModeVersion === oldName 
                  ? newName 
                  : state.currentMixedModeVersion,
              };
            }
            case 'matrix': {
              const oldVersion = state.matrixModeVersions[oldName];
              if (!oldVersion) return state;
              
              const newVersions = { ...state.matrixModeVersions };
              delete newVersions[oldName];
              newVersions[newName] = { ...oldVersion, name: newName };
              
              return {
                matrixModeVersions: newVersions,
                currentMatrixModeVersion: state.currentMatrixModeVersion === oldName 
                  ? newName 
                  : state.currentMatrixModeVersion,
              };
            }
            default:
              return state;
          }
        });
      },

      setCurrentVersion: (type: VersionType, name: string) => {
        get().switchToVersion(type, name);
      },

      getCurrentVersionData: (type: VersionType) => {
        const state = get();
        switch (type) {
          case 'default':
            return state.defaultVersions[state.currentDefaultVersion] || null;
          case 'groupMode':
            return state.groupModeVersions[state.currentGroupModeVersion] || null;
          case 'mixedMode':
            return state.mixedModeVersions[state.currentMixedModeVersion] || null;
          case 'matrix':
            return state.matrixModeVersions[state.currentMatrixModeVersion] || null;
          default:
            return null;
        }
      },

      setNewVersionName: (type: VersionType, name: string) => {
        set((state) => {
          switch (type) {
            case 'default':
              return { newDefaultVersionName: name };
            case 'groupMode':
              return { newGroupModeVersionName: name };
            case 'mixedMode':
              return { newMixedModeVersionName: name };
            case 'matrix':
              return { newMatrixModeVersionName: name };
            default:
              return state;
          }
        });
      },

      clearNewVersionName: (type: VersionType) => {
        get().setNewVersionName(type, '');
      },

      getVersionList: (type: VersionType) => {
        const state = get();
        switch (type) {
          case 'default':
            return Object.keys(state.defaultVersions);
          case 'groupMode':
            return Object.keys(state.groupModeVersions);
          case 'mixedMode':
            return Object.keys(state.mixedModeVersions);
          case 'matrix':
            return Object.keys(state.matrixModeVersions);
          default:
            return [];
        }
      },

      getVersionCount: (type: VersionType) => {
        return get().getVersionList(type).length;
      },

      hasVersion: (type: VersionType, name: string) => {
        return get().getVersionList(type).includes(name);
      },

      exportVersions: (type: VersionType) => {
        const state = get();
        let exportData;
        
        switch (type) {
          case 'default':
            exportData = {
              type: 'default',
              versions: state.defaultVersions,
              currentVersion: state.currentDefaultVersion,
              exportTime: Date.now(),
            };
            break;
          case 'groupMode':
            exportData = {
              type: 'groupMode',
              versions: state.groupModeVersions,
              currentVersion: state.currentGroupModeVersion,
              exportTime: Date.now(),
            };
            break;
          case 'mixedMode':
            exportData = {
              type: 'mixedMode',
              versions: state.mixedModeVersions,
              currentVersion: state.currentMixedModeVersion,
              exportTime: Date.now(),
            };
            break;
          case 'matrix':
            exportData = {
              type: 'matrix',
              versions: state.matrixModeVersions,
              currentVersion: state.currentMatrixModeVersion,
              exportTime: Date.now(),
            };
            break;
          default:
            exportData = {};
        }
        
        return JSON.stringify(exportData, null, 2);
      },

      importVersions: (type: VersionType, data: string) => {
        try {
          const importedData = JSON.parse(data);
          
          if (importedData.type !== type) {
            console.warn('版本类型不匹配');
            return false;
          }
          
          set((state) => {
            switch (type) {
              case 'default':
                return {
                  defaultVersions: importedData.versions || {},
                  currentDefaultVersion: importedData.currentVersion || state.currentDefaultVersion,
                };
              case 'groupMode':
                return {
                  groupModeVersions: importedData.versions || {},
                  currentGroupModeVersion: importedData.currentVersion || state.currentGroupModeVersion,
                };
              case 'mixedMode':
                return {
                  mixedModeVersions: importedData.versions || {},
                  currentMixedModeVersion: importedData.currentVersion || state.currentMixedModeVersion,
                };
              case 'matrix':
                return {
                  matrixModeVersions: importedData.versions || {},
                  currentMatrixModeVersion: importedData.currentVersion || state.currentMatrixModeVersion,
                };
              default:
                return state;
            }
          });
          
          return true;
        } catch (error) {
          console.error('导入版本数据失败:', error);
          return false;
        }
      },

      clearAllVersions: (type: VersionType) => {
        set((state) => {
          switch (type) {
            case 'default':
              return {
                defaultVersions: { '默认版本': DEFAULT_VERSION_STATE.defaultVersions['默认版本'] },
                currentDefaultVersion: '默认版本',
              };
            case 'groupMode':
              return {
                groupModeVersions: { '默认分组': DEFAULT_VERSION_STATE.groupModeVersions['默认分组'] },
                currentGroupModeVersion: '默认分组',
              };
            case 'mixedMode':
              return {
                mixedModeVersions: { '默认混合': DEFAULT_VERSION_STATE.mixedModeVersions['默认混合'] },
                currentMixedModeVersion: '默认混合',
              };
            case 'matrix':
              return {
                matrixModeVersions: { '默认矩阵': DEFAULT_VERSION_STATE.matrixModeVersions['默认矩阵'] },
                currentMatrixModeVersion: '默认矩阵',
              };
            default:
              return state;
          }
        });
      },

      resetVersionState: () => set(DEFAULT_VERSION_STATE),

      syncBetweenVersions: (fromType: VersionType, toType: VersionType, versionName?: string) => {
        const sourceData = get().getCurrentVersionData(fromType);
        
        if (!sourceData) return;
        
        const targetName = versionName || `从${fromType}同步_${Date.now()}`;
        get().saveVersion(toType, targetName, sourceData.data);
      },

      // UI状态操作（整合自uiStore.ts）
      setActivePanel: (panel) => set({ activePanel: panel }),
      setSettingsSubPage: (subPage) => set({ settingsSubPage: subPage }),
      setSingleColorMode: (color) => set({ singleColorMode: color }),
      exitSingleColorMode: () => set({ singleColorMode: null }),
      setShowSpecificGroup: (group) => set({ showSpecificGroup: group }),
      setIsClient: (isClient) => set({ isClient }),
      resetUIState: () => set(DEFAULT_UI_STATE),

      // 提示操作
      showToastNotification: (message, type = 'info') => {
        set({ 
          toastState: {
            message, 
            type, 
            show: true,
            duration: 3000
          }
        });
        
        // 自动隐藏toast
        setTimeout(() => {
          set((state) => ({ 
            toastState: { ...state.toastState, show: false }
          }));
        }, 3000);
      },

      // 模式切换
      switchMode: (mode) =>
        set(() => ({ currentActiveMode: mode })),

      // 交互状态操作
      setHoverInfo: (info) =>
        set((state) => ({
          interactionState: { ...state.interactionState, hoverInfo: info }
        })),

      setDragging: (isDragging) =>
        set((state) => ({
          interactionState: { ...state.interactionState, isDragging }
        })),

      // 提示操作
      showToast: (message, type, duration = 3000) =>
        set(() => ({
          toastState: { message, type, show: true, duration }
        })),

      hideToast: () =>
        set((state) => ({
          toastState: { ...state.toastState, show: false }
        })),

      // 重置操作
      resetBusinessData: () =>
        set(() => ({
          clickedCells: new Set<string>(),
          currentActiveMode: 'default',
          interactionState: DEFAULT_INTERACTION_STATE,
          toastState: DEFAULT_TOAST_STATE
        }))
    }),
    {
      name: 'business-data-store',
      version: 2, // 版本升级，包含UI状态和版本管理
      // 不持久化某些运行时状态
      partialize: (state) => ({
        ...state,
        interactionState: DEFAULT_INTERACTION_STATE, // 交互状态不持久化
        toastState: DEFAULT_TOAST_STATE // 提示状态不持久化
      }),
      // 自定义序列化处理Set类型
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          try {
            const data = JSON.parse(str);
            
            // 强化Set类型处理
            if (data.state) {
              // 确保clickedCells始终是Set类型
              if (data.state.clickedCells) {
                if (Array.isArray(data.state.clickedCells)) {
                  data.state.clickedCells = new Set(data.state.clickedCells);
                } else if (typeof data.state.clickedCells === 'object' && data.state.clickedCells !== null) {
                  // 处理可能的错误格式
                  const values = Object.values(data.state.clickedCells);
                  data.state.clickedCells = new Set(values.filter(v => typeof v === 'string'));
                } else {
                  // 如果格式完全错误，初始化为空Set
                  data.state.clickedCells = new Set<string>();
                }
              } else {
                // 如果没有clickedCells，初始化为空Set
                data.state.clickedCells = new Set<string>();
              }
              
              // 处理interactionState中的selectedCells
              if (data.state.interactionState && data.state.interactionState.selectedCells) {
                if (Array.isArray(data.state.interactionState.selectedCells)) {
                  data.state.interactionState.selectedCells = new Set(data.state.interactionState.selectedCells);
                } else {
                  data.state.interactionState.selectedCells = new Set<string>();
                }
              }
            }
            
            return data;
          } catch (error) {
            console.warn('Failed to parse stored data, falling back to defaults:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          try {
            const valueToStore = {
              ...value,
              state: {
                ...value.state,
                // 将clickedCells从Set转换为数组进行存储
                clickedCells: value.state.clickedCells instanceof Set 
                  ? Array.from(value.state.clickedCells) 
                  : [],
                // 处理interactionState中的selectedCells
                interactionState: {
                  ...value.state.interactionState,
                  selectedCells: value.state.interactionState.selectedCells instanceof Set
                    ? Array.from(value.state.interactionState.selectedCells)
                    : []
                }
              }
            };
            localStorage.setItem(name, JSON.stringify(valueToStore));
          } catch (error) {
            console.warn('Failed to store data:', error);
          }
        },
        removeItem: (name) => localStorage.removeItem(name)
      }
    }
  )
);

// 选择器函数
export const useClickedCells = () => useBusinessDataStore((state) => state.clickedCells);
export const useCurrentMode = () => useBusinessDataStore((state) => state.currentActiveMode);
export const useHoverInfo = () => useBusinessDataStore((state) => state.interactionState.hoverInfo);
export const useToastState = () => useBusinessDataStore((state) => state.toastState);
export const useVersionManagement = () => useBusinessDataStore((state) => ({
  defaultVersions: state.defaultVersions,
  currentDefaultVersion: state.currentDefaultVersion,
  newDefaultVersionName: state.newDefaultVersionName,
  groupModeVersions: state.groupModeVersions,
  currentGroupModeVersion: state.currentGroupModeVersion,
  newGroupModeVersionName: state.newGroupModeVersionName,
  mixedModeVersions: state.mixedModeVersions,
  currentMixedModeVersion: state.currentMixedModeVersion,
  newMixedModeVersionName: state.newMixedModeVersionName,
  matrixModeVersions: state.matrixModeVersions,
  currentMatrixModeVersion: state.currentMatrixModeVersion,
  newMatrixModeVersionName: state.newMatrixModeVersionName
}));

// 工具函数
export const getCellKey = (x: number, y: number): string => `${x},${y}`;
export const isCellClicked = (cellKey: string): boolean => {
  return useBusinessDataStore.getState().clickedCells.has(cellKey);
};

// UI状态选择器（整合自uiStore.ts）
export const useActivePanel = () => useBusinessDataStore((state) => state.activePanel);
export const useSettingsSubPage = () => useBusinessDataStore((state) => state.settingsSubPage);
export const useSingleColorMode = () => useBusinessDataStore((state) => state.singleColorMode);
export const useIsClient = () => useBusinessDataStore((state) => state.isClient);

// 版本管理选择器（整合自versionStore.ts）
export const useCurrentVersions = () => useBusinessDataStore((state) => ({
  default: state.currentDefaultVersion,
  groupMode: state.currentGroupModeVersion,
  mixedMode: state.currentMixedModeVersion,
  matrix: state.currentMatrixModeVersion,
}));

export const useVersionList = (type: VersionType) => useBusinessDataStore((state) => {
  switch (type) {
    case 'default':
      return Object.keys(state.defaultVersions);
    case 'groupMode':
      return Object.keys(state.groupModeVersions);
    case 'mixedMode':
      return Object.keys(state.mixedModeVersions);
    case 'matrix':
      return Object.keys(state.matrixModeVersions);
    default:
      return [];
  }
});

export const useNewVersionNames = () => useBusinessDataStore((state) => ({
  default: state.newDefaultVersionName,
  groupMode: state.newGroupModeVersionName,
  mixedMode: state.newMixedModeVersionName,
  matrix: state.newMatrixModeVersionName,
}));

export const useVersionActions = () => useBusinessDataStore((state) => ({
  saveVersion: state.saveVersion,
  switchToVersion: state.switchToVersion,
  deleteVersion: state.deleteVersion,
  renameVersion: state.renameVersion,
  setCurrentVersion: state.setCurrentVersion,
  getCurrentVersionData: state.getCurrentVersionData,
  setNewVersionName: state.setNewVersionName,
  clearNewVersionName: state.clearNewVersionName,
  getVersionList: state.getVersionList,
  getVersionCount: state.getVersionCount,
  hasVersion: state.hasVersion,
  exportVersions: state.exportVersions,
  importVersions: state.importVersions,
  clearAllVersions: state.clearAllVersions,
  resetVersionState: state.resetVersionState,
  syncBetweenVersions: state.syncBetweenVersions,
})); 