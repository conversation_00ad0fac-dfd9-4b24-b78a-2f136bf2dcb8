/**
 * 动态样式Store - 管理界面样式和显示配置
 * 🎯 核心职责：字体、边距、形状、模式、缩放等动态样式
 * 📦 整合常量：UI基础配置、显示控制默认值、样式配置等
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 样式配置常量
export type CellShape = 'square' | 'rounded' | 'circle';
export type DisplayMode = 'number' | 'coordinate' | 'hidden';

// 单元格形状选项
export const CELL_SHAPES: readonly CellShape[] = ['rounded', 'circle', 'square'] as const;

// 显示模式选项
export const DISPLAY_MODES: readonly DisplayMode[] = ['hidden', 'number', 'coordinate'] as const;

// 圆形缩放配置常量
export const CIRCLE_SCALE_CONFIG = {
  DEFAULT_ENABLED: true,
  DEFAULT_SCALE_FACTOR: 1.4,
  MIN_SCALE_FACTOR: 1.0,
  MAX_SCALE_FACTOR: 2.0,
} as const;

// 基础UI配置默认值
export const DEFAULT_UI_CONFIG = {
  fontSize: 14,
  matrixMargin: 20,
  gridColor: 'gray',
  cellShape: 'rounded' as CellShape,
  displayMode: 'hidden' as DisplayMode,
} as const;

// 显示控制默认值
export const DEFAULT_DISPLAY_CONFIG = {
  showAllColors: true,
  showAllNumbers: true,
  showAllLevel1: false,
  showAllLevel2: false,
  showAllLevel3: false,
  showAllLevel4: false,
  enableCircleScale: true,
  circleScaleFactor: 1.4,
  enableVirtualization: false, // 🚨 格子变形bug修复：默认禁用虚拟滚动
} as const;

// 操作限制规则
export const OPERATION_RESTRICTIONS = {
  MIN_FONT_SIZE: 8,
  MAX_FONT_SIZE: 20,
  MIN_MATRIX_MARGIN: 0,
  MAX_MATRIX_MARGIN: 20,
  MIN_CELL_SIZE: 12,
  MAX_CELL_SIZE: 48,
} as const;

// 形状图标映射
export const SHAPE_ICONS = {
  square: { icon: '□', tooltip: '方形' },
  rounded: { icon: '⬜', tooltip: '圆角' },
  circle: { icon: '●', tooltip: '圆形' },
} as const;

// 显示模式图标映射
export const DISPLAY_MODE_ICONS = {
  number: { icon: '123', tooltip: '显示数字' },
  coordinate: { icon: 'XY', tooltip: '显示坐标' },
  hidden: { icon: '×', tooltip: '隐藏内容' },
} as const;

export interface DynamicStyleState {
  // 动态样式配置
  fontSize: number;
  matrixMargin: number;
  cellShape: CellShape;
  displayMode: DisplayMode;
  enableCircleScale: boolean;
  circleScaleFactor: number;
  
  // 网格渲染控制（🚨 格子变形bug修复）
  enableVirtualization: boolean;
  
  // 网格显示控制
  showAllNumbers: boolean;
  showAllColors: boolean;
  
  // 全局级别控制
  showAllLevel1: boolean;
  showAllLevel2: boolean;
  showAllLevel3: boolean;
  showAllLevel4: boolean;
  
  // 动态样式操作
  setFontSize: (size: number) => void;
  setMatrixMargin: (margin: number) => void;
  setCellShape: (shape: CellShape) => void;
  setDisplayMode: (mode: DisplayMode) => void;
  toggleCircleScale: () => void;
  setCircleScaleFactor: (factor: number) => void;
  
  // 网格渲染控制（🚨 格子变形bug修复）
  toggleVirtualization: () => void;
  
  // 全局显示控制
  setShowAllNumbers: (show: boolean) => void;
  setShowAllColors: (show: boolean) => void;
  setShowAllLevel1: (show: boolean) => void;
  setShowAllLevel2: (show: boolean) => void;
  setShowAllLevel3: (show: boolean) => void;
  setShowAllLevel4: (show: boolean) => void;
  toggleAllLevels: (show: boolean) => void;
  
  // 常量访问器
  getCellShapes: () => readonly CellShape[];
  getDisplayModes: () => readonly DisplayMode[];
  getShapeIcon: (shape: CellShape) => { icon: string; tooltip: string };
  getDisplayModeIcon: (mode: DisplayMode) => { icon: string; tooltip: string };
  validateFontSize: (size: number) => number;
  validateMargin: (margin: number) => number;
  resetToDefaults: () => void;
}

export const useDynamicStyleStore = create<DynamicStyleState>()(
  persist(
    (set, get) => ({
      // 初始状态
      fontSize: DEFAULT_UI_CONFIG.fontSize,
      matrixMargin: DEFAULT_UI_CONFIG.matrixMargin,
      cellShape: DEFAULT_UI_CONFIG.cellShape,
      displayMode: DEFAULT_UI_CONFIG.displayMode,
      enableCircleScale: DEFAULT_DISPLAY_CONFIG.enableCircleScale,
      circleScaleFactor: DEFAULT_DISPLAY_CONFIG.circleScaleFactor,
      enableVirtualization: DEFAULT_DISPLAY_CONFIG.enableVirtualization,
      showAllNumbers: DEFAULT_DISPLAY_CONFIG.showAllNumbers,
      showAllColors: DEFAULT_DISPLAY_CONFIG.showAllColors,
      showAllLevel1: DEFAULT_DISPLAY_CONFIG.showAllLevel1,
      showAllLevel2: DEFAULT_DISPLAY_CONFIG.showAllLevel2,
      showAllLevel3: DEFAULT_DISPLAY_CONFIG.showAllLevel3,
      showAllLevel4: DEFAULT_DISPLAY_CONFIG.showAllLevel4,

      // 动态样式操作
      setFontSize: (size) => 
        set(() => ({ 
          fontSize: Math.min(Math.max(size, OPERATION_RESTRICTIONS.MIN_FONT_SIZE), OPERATION_RESTRICTIONS.MAX_FONT_SIZE) 
        })),

      setMatrixMargin: (margin) => 
        set(() => ({ 
          matrixMargin: Math.min(Math.max(margin, OPERATION_RESTRICTIONS.MIN_MATRIX_MARGIN), OPERATION_RESTRICTIONS.MAX_MATRIX_MARGIN) 
        })),

      setCellShape: (shape) => set(() => ({ cellShape: shape })),

      setDisplayMode: (mode) => set(() => ({ displayMode: mode })),

      toggleCircleScale: () =>
        set((state) => ({ enableCircleScale: !state.enableCircleScale })),

      setCircleScaleFactor: (factor) =>
        set(() => ({ 
          circleScaleFactor: Math.min(Math.max(factor, CIRCLE_SCALE_CONFIG.MIN_SCALE_FACTOR), CIRCLE_SCALE_CONFIG.MAX_SCALE_FACTOR) 
        })),

      // 网格渲染控制（🚨 格子变形bug修复）
      toggleVirtualization: () =>
        set((state) => ({ enableVirtualization: !state.enableVirtualization })),

      // 全局显示控制
      setShowAllNumbers: (show) => set(() => ({ showAllNumbers: show })),

      setShowAllColors: (show) => set(() => ({ showAllColors: show })),

      setShowAllLevel1: (show) => set(() => ({ showAllLevel1: show })),

      setShowAllLevel2: (show) => set(() => ({ showAllLevel2: show })),

      setShowAllLevel3: (show) => set(() => ({ showAllLevel3: show })),

      setShowAllLevel4: (show) => set(() => ({ showAllLevel4: show })),

      toggleAllLevels: (show) =>
        set(() => ({
          showAllLevel1: show,
          showAllLevel2: show,
          showAllLevel3: show,
          showAllLevel4: show,
        })),

      // 常量访问器
      getCellShapes: () => CELL_SHAPES,
      getDisplayModes: () => DISPLAY_MODES,
      getShapeIcon: (shape) => SHAPE_ICONS[shape],
      getDisplayModeIcon: (mode) => DISPLAY_MODE_ICONS[mode],
      validateFontSize: (size) => Math.min(Math.max(size, OPERATION_RESTRICTIONS.MIN_FONT_SIZE), OPERATION_RESTRICTIONS.MAX_FONT_SIZE),
      validateMargin: (margin) => Math.min(Math.max(margin, OPERATION_RESTRICTIONS.MIN_MATRIX_MARGIN), OPERATION_RESTRICTIONS.MAX_MATRIX_MARGIN),
      
      resetToDefaults: () =>
        set(() => ({
          fontSize: DEFAULT_UI_CONFIG.fontSize,
          matrixMargin: DEFAULT_UI_CONFIG.matrixMargin,
          cellShape: DEFAULT_UI_CONFIG.cellShape,
          displayMode: DEFAULT_UI_CONFIG.displayMode,
          enableCircleScale: DEFAULT_DISPLAY_CONFIG.enableCircleScale,
          circleScaleFactor: DEFAULT_DISPLAY_CONFIG.circleScaleFactor,
          enableVirtualization: DEFAULT_DISPLAY_CONFIG.enableVirtualization,
          showAllNumbers: DEFAULT_DISPLAY_CONFIG.showAllNumbers,
          showAllColors: DEFAULT_DISPLAY_CONFIG.showAllColors,
          showAllLevel1: DEFAULT_DISPLAY_CONFIG.showAllLevel1,
          showAllLevel2: DEFAULT_DISPLAY_CONFIG.showAllLevel2,
          showAllLevel3: DEFAULT_DISPLAY_CONFIG.showAllLevel3,
          showAllLevel4: DEFAULT_DISPLAY_CONFIG.showAllLevel4,
        })),
    }),
    {
      name: 'dynamic-style-store',
      version: 1,
    }
  )
);

// 选择器函数
export const useFontSize = () => useDynamicStyleStore((state) => state.fontSize);
export const useMatrixMargin = () => useDynamicStyleStore((state) => state.matrixMargin);
export const useCellShape = () => useDynamicStyleStore((state) => state.cellShape);
export const useDisplayMode = () => useDynamicStyleStore((state) => state.displayMode);
export const useCircleScale = () => useDynamicStyleStore((state) => ({
  enabled: state.enableCircleScale,
  factor: state.circleScaleFactor,
}));
export const useGlobalDisplay = () => useDynamicStyleStore((state) => ({
  showAllNumbers: state.showAllNumbers,
  showAllColors: state.showAllColors,
  showAllLevel1: state.showAllLevel1,
  showAllLevel2: state.showAllLevel2,
  showAllLevel3: state.showAllLevel3,
  showAllLevel4: state.showAllLevel4,
})); 