/**
 * Zustand状态管理Store统一导出
 * 🎯 核心价值：集中管理5个核心状态Store，简化导入和维护
 * ⚡ 性能优化：统一导出减少重复导入
 * 🔄 最终架构：只保留5个专业化Store，完全清理重复状态
 * ✅ Phase 4.7.1: 架构职责分离完成 - 常量、工具函数、状态管理分离
 */

// === 核心Store架构导出 ===

// 🎨 样式Store - 状态管理（已精简）
export {
  useStyleStore,
  useMatrixStyles,
  useControlPanelStyles,
  useButtonStyles,
  useColorScheme,
  useCurrentTheme,
  useShowBlackCells,
  type StyleState,
} from './styleStore';

// 🔧 动态样式Store - 字体、边距、形状、显示模式等
export {
  useDynamicStyleStore,
  useFontSize,
  useMatrixMargin,
  useCellShape,
  useDisplayMode,
  useCircleScale,
  useGlobalDisplay,
  type DynamicStyleState,
  type CellShape,
  type DisplayMode,
  CELL_SHAPES,
  DISPLAY_MODES,
  CIRCLE_SCALE_CONFIG,
  DEFAULT_UI_CONFIG,
  DEFAULT_DISPLAY_CONFIG,
  OPERATION_RESTRICTIONS,
} from './dynamicStyleStore';

// 📊 基础数据Store - 颜色坐标数据管理
export {
  useBasicDataStore,
  useColorCoordinates,
  useColorValues,
  useColorVisibility,
  useBlackCellData,
  type BasicDataState,
  type ColorLevel,
  type ColorValue,
  type ColorVisibility,
  type BlackCellData,
  COLOR_LEVELS,
  DEFAULT_COLOR_VALUES,
  DEFAULT_COLOR_VISIBILITY,
  getAllColorTypes,
} from './basicDataStore';

// 🔀 组合数据Store - 组合模式数据管理
export {
  useCombinationDataStore,
  type CombinationDataState,
  GROUP_TYPES,
  MODE_TYPES,
  COMBINATION_RULES,
} from './combinationDataStore';

// 💼 业务数据Store - 交互状态和版本管理
export {
  useBusinessDataStore,
  type BusinessDataState,
  type InteractionState,
  type VersionData,
  type ToastState,
} from './businessDataStore';

// === 分离架构导出 ===

// 🎨 颜色常量和类型
export type { ColorType, BasicColorType } from '../constants/colors';
export {
  COLOR_CSS_MAP,
  BLACK_CSS_MAP,
  COLOR_NAMES,
  COLOR_SHORT_NAMES,
  COLOR_NUMBER_MAP,
  COLOR_PRIORITY_ORDER,
} from '../constants/colors';

// 🔧 样式常量和类型
export type { 
  ButtonVariant,
  ButtonSize,
  MatrixStyles,
  ControlPanelStyles,
  ButtonStyles,
  ColorScheme,
} from '../constants/styles';
export {
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
  BUTTON_STYLES,
  TAB_STYLES,
  INPUT_STYLES,
  GRID_STYLES,
  DEFAULT_MATRIX_STYLES,
  DEFAULT_CONTROL_PANEL_STYLES,
  DEFAULT_BUTTON_STYLES,
  DEFAULT_COLOR_SCHEME,
} from '../constants/styles';

// 🛠️ 颜色工具函数
export {
  getColorCSS,
  getColorCSSMap,
  getBlackCSS,
  getColorName,
  getColorDisplayName,
  getColorNumber,
  getColorPriority,
  getCellStyle,
  getTabStyle as getTabStyleUtil,
} from '../utils/colorUtils';

// 🔘 按钮工具函数
export {
  getAdvancedButtonStyle,
  getActiveButtonStyle,
  getColorButtonStyle,
  getGridButtonStyle,
  getModeButtonStyle,
  getDangerButtonStyle,
  getSuccessButtonStyle,
  getButtonStyle,
  getTabStyle,
  getGridStyle,
} from '../utils/buttonUtils';

// === 向后兼容性导出 ===

// 为了保持向后兼容，提供一些别名导出
export { useStyleStore as useColorStore } from './styleStore';
export { useBasicDataStore as useGridStore } from './basicDataStore';
export { useBusinessDataStore as useInteractionStore } from './businessDataStore';
export { useBusinessDataStore as useUIStore } from './businessDataStore';
export { useBusinessDataStore as useVersionStore } from './businessDataStore'; 