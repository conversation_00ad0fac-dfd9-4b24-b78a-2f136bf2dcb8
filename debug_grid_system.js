/**
 * 网格系统功能测试脚本
 * 用于验证格子控制按钮和底色显示的修复效果
 */

// 在浏览器控制台中运行此脚本来测试功能

function testGridSystemFunctionality() {
    console.log('🧪 开始网格系统功能测试...');
    
    // 1. 检查stores是否正确挂载
    if (!window.stores) {
        console.error('❌ window.stores未找到，请确保应用已加载');
        return;
    }
    
    console.log('✅ Stores已挂载:', Object.keys(window.stores));
    
    // 2. 测试颜色可见性状态
    const basicData = window.stores.basicData;
    if (basicData && basicData.colorVisibility) {
        console.log('📊 当前颜色可见性状态:');
        Object.entries(basicData.colorVisibility).forEach(([color, visibility]) => {
            console.log(`  ${color}:`, visibility);
        });
    }
    
    // 3. 检查红色级别状态（重点测试对象）
    const redVisibility = basicData?.colorVisibility?.red;
    if (redVisibility) {
        console.log('🔴 红色级别详细状态:');
        [1, 2, 3, 4].forEach(level => {
            const levelKey = `showLevel${level}`;
            const value = redVisibility[levelKey];
            console.log(`  Level ${level} (${levelKey}): ${value} (${typeof value})`);
        });
    }
    
    // 4. 模拟按钮点击测试
    console.log('🔘 模拟按钮点击测试...');
    
    // 查找红色Level1按钮
    const redLevel1Button = document.querySelector('button[data-color="red"][data-level="1"]') ||
                           Array.from(document.querySelectorAll('button')).find(btn => 
                               btn.textContent?.includes('红1') || btn.textContent?.includes('R1')
                           );
    
    if (redLevel1Button) {
        console.log('✅ 找到红色Level1按钮，模拟点击...');
        redLevel1Button.click();
        
        setTimeout(() => {
            console.log('🔄 点击后状态检查...');
            const newRedVisibility = window.stores?.basicData?.colorVisibility?.red;
            if (newRedVisibility) {
                console.log('🔴 点击后红色可见性:', newRedVisibility);
            }
        }, 200);
    } else {
        console.warn('⚠️ 未找到红色Level1按钮');
    }
    
    // 5. 检查网格单元格渲染状态
    console.log('🎯 检查网格单元格渲染状态...');
    
    // 查找特定坐标的格子
    const testCoords = [
        { x: 8, y: 0, desc: '红色Level1' },
        { x: 4, y: 0, desc: '红色Level2' },
        { x: 2, y: 0, desc: '红色Level3' },
        { x: 1, y: 0, desc: '红色Level4' }
    ];
    
    testCoords.forEach(({ x, y, desc }) => {
        const cellSelector = `[data-x="${x}"][data-y="${y}"]`;
        const cell = document.querySelector(cellSelector);
        
        if (cell) {
            const styles = window.getComputedStyle(cell);
            const bgColor = styles.backgroundColor;
            const classes = cell.className;
            
            console.log(`📍 坐标(${x},${y}) ${desc}:`, {
                backgroundColor: bgColor,
                classes: classes,
                isRed: bgColor.includes('rgb') && !bgColor.includes('128'), // 简单检查是否为红色
                element: cell
            });
        } else {
            console.warn(`⚠️ 未找到坐标(${x},${y})的格子`);
        }
    });
    
    // 6. 性能测试
    console.log('⚡ 性能测试...');
    const startTime = performance.now();
    
    // 模拟多次状态切换
    for (let i = 0; i < 10; i++) {
        if (redLevel1Button) {
            redLevel1Button.click();
        }
    }
    
    const endTime = performance.now();
    console.log(`📊 10次按钮点击耗时: ${(endTime - startTime).toFixed(2)}ms`);
    
    console.log('🎉 网格系统功能测试完成！');
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 等待页面加载完成后运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testGridSystemFunctionality, 2000);
        });
    } else {
        setTimeout(testGridSystemFunctionality, 2000);
    }
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testGridSystemFunctionality };
}

console.log('📝 网格系统测试脚本已加载');
console.log('💡 手动运行测试: testGridSystemFunctionality()');
