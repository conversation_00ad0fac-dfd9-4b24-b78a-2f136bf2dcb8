# Debug Phase: 颜色级别显示异常深度修复

## 任务背景
**问题发现**: 2025年6月24日 20:38  
**用户反馈**: 红色2级格子显示正常，红色1级、3级透明消失，4级未激活  
**复杂度认知**: 经过两轮错误分析后，发现问题涉及颜色映射数据、撇捺分组、横竖分组等复杂业务关系交叉影响  
**当前状态**: 问题复杂化，需要深度分析和系统性修复

---

## Phase Debug-1: 深度诊断阶段 【P0-紧急】

### Debug-1.1: 数据一致性诊断工具 ✅ 已完成
**目标**: 创建完整的数据一致性检查系统  
**时间估计**: 2小时  
**完成时间**: 2025年6月24日 21:08

**具体任务**:
- [x] 创建 `utils/dataConsistencyChecker.ts` 诊断工具
- [x] 验证 `AVAILABLE_LEVELS` ↔ `DEFAULT_COLOR_VISIBILITY` ↔ `实际坐标数据` 三者一致性
- [x] 检查橙色、绿色、蓝色、粉色的level2数据缺失问题
- [x] 生成详细的数据不一致报告

**预期发现**:
```typescript
// 预期问题：orange等颜色的level2不一致
AVAILABLE_LEVELS.orange = [1, 3, 4]           // ✅ 正确：没有level2
DEFAULT_COLOR_VISIBILITY.orange.showLevel2    // ❌ 问题：undefined但被当作true处理
orangeCoordinates.level2                       // ❌ 问题：不存在但可能被引用
```

**验收标准**:
- [ ] 生成完整的8种颜色数据一致性报告
- [ ] 识别所有数据不匹配点
- [ ] 提供具体的修复建议

### Debug-1.2: 渲染逻辑调试追踪 ✅ 已完成
**目标**: 在关键渲染路径添加详细调试日志  
**时间估计**: 1.5小时  
**完成时间**: 2025年6月24日 21:08

**具体任务**:
- [x] 在 `usePageLogic.getCellStyle` 添加条件调试日志
- [x] 在 `isLevelVisible` 函数添加详细状态追踪
- [x] 在 `colorIndex.getAllColorInfo` 添加数据返回值日志
- [x] 创建特定坐标的详细渲染路径追踪

**调试重点坐标**:
```typescript
// 重点调试红色坐标
const DEBUG_COORDS = [
  { x: 8, y: 0, level: 1 },    // 红色1级 - 应该显示但透明消失
  { x: 4, y: 0, level: 2 },    // 红色2级 - 显示正常
  { x: 2, y: 0, level: 3 },    // 红色3级 - 应该显示但透明消失
  { x: 1, y: 0, level: 4 },    // 红色4级 - 未激活问题
];
```

**验收标准**:
- [ ] 能够追踪任意坐标的完整渲染路径
- [ ] 识别红色1级、3级透明消失的确切原因
- [ ] 找到红色4级未激活的根本问题

### Debug-1.3: UI状态与数据状态对比验证
**目标**: 检查BasicDataPanel UI状态与实际数据状态的一致性  
**时间估计**: 1小时  

**具体任务**:
- [ ] 添加 `BasicDataPanel` 状态监控日志
- [ ] 比较 `colorVisibility[color][levelKey]` 与实际渲染结果
- [ ] 检查 `getAvailableLevels` 函数返回值正确性
- [ ] 验证级别按钮点击事件的状态传播

**验收标准**:
- [ ] UI按钮状态与Store状态100%一致
- [ ] 级别切换操作正确更新所有相关状态
- [ ] 不存在级别的按钮正确禁用或隐藏

---

## Phase Debug-2: 交叉影响分析阶段 【P0-紧急】

### Debug-2.1: 分组逻辑影响分析
**目标**: 分析撇捺分组vs横竖分组对级别显示的影响  
**时间估计**: 2小时  

**具体任务**:
- [ ] 分析 `showSpecificGroup` 筛选对级别显示的影响
- [ ] 检查撇捺分组(1-10) vs 横竖分组(11-44)的数据处理差异
- [ ] 验证 `combinationDataStore` 分组状态对颜色渲染的影响
- [ ] 测试不同分组模式下的级别显示一致性

**重点检查**:
```typescript
// 检查分组交叉影响
combinationDataStore.selectedGroups vs basicDataStore.colorVisibility
pieNaConfig.colors vs zhuHengConfig.colors 的处理差异
showSpecificGroup !== null 时的级别过滤逻辑
```

**验收标准**:
- [ ] 明确分组逻辑对级别显示的具体影响
- [ ] 识别任何分组相关的级别显示bug
- [ ] 确保分组切换不影响级别可见性

### Debug-2.2: 性能优化缓存影响检查
**目标**: 检查性能优化是否导致状态更新延迟  
**时间估计**: 1小时  

**具体任务**:
- [ ] 检查 `usePerformanceOptimized` Hook的缓存逻辑
- [ ] 验证 `colorIndex` memoization是否阻止状态更新
- [ ] 测试快速级别切换时的响应性
- [ ] 检查 `useMemo` 和 `useCallback` 依赖数组正确性

**验收标准**:
- [ ] 确认性能优化不影响状态实时更新
- [ ] 级别切换立即反映到UI和渲染
- [ ] 缓存失效机制正确工作

### Debug-2.3: Store状态同步验证
**目标**: 验证6个Store之间的状态同步正确性  
**时间估计**: 1.5小时  

**具体任务**:
- [ ] 监控 `basicDataStore.colorVisibility` 变更传播
- [ ] 检查 `dynamicStyleStore` 与 `basicDataStore` 的交互
- [ ] 验证 `businessDataStore` 交互状态不影响颜色显示
- [ ] 测试Store状态的LocalStorage持久化正确性

**验收标准**:
- [ ] 所有Store状态变更正确同步
- [ ] 状态持久化与恢复正确
- [ ] 无状态冲突或覆盖问题

---

## Phase Debug-3: 问题修复阶段 【P1-高优先级】

### Debug-3.1: 数据模型一致性修复
**目标**: 修复所有数据不一致问题  
**时间估计**: 3小时  

**具体任务**:
- [ ] 统一 `AVAILABLE_LEVELS` 和 `DEFAULT_COLOR_VISIBILITY`
- [ ] 创建数据Schema验证系统
- [ ] 修复橙色等颜色的level2相关问题
- [ ] 建立数据一致性自动检查机制

**修复策略**:
```typescript
// 策略1：严格按AVAILABLE_LEVELS定义可见性
const generateConsistentVisibility = (colorType: BasicColorType) => {
  const levels = AVAILABLE_LEVELS[colorType];
  const visibility: ColorVisibility = { showCells: true };
  
  [1, 2, 3, 4].forEach(level => {
    if (levels.includes(level)) {
      visibility[`showLevel${level}`] = true;
    }
    // 不包含的级别不设置对应属性
  });
  
  return visibility;
};
```

**验收标准**:
- [ ] 所有颜色的可见性配置与可用级别100%一致
- [ ] 数据Schema验证通过
- [ ] 不存在级别的相关操作正确处理

### Debug-3.2: 级别可见性逻辑重构
**目标**: 重写isLevelVisible函数，正确处理undefined级别  
**时间估计**: 2小时  

**具体任务**:
- [ ] 重构 `hooks/useFormHandlers.ts` 中的 `isLevelVisible` 函数
- [ ] 明确处理undefined级别的默认行为
- [ ] 添加级别存在性预检查
- [ ] 优化级别可见性判断性能

**新实现策略**:
```typescript
const isLevelVisible = useCallback((visibility: ColorVisibility, level: number, colorType: BasicColorType) => {
  // 首先检查级别是否存在
  const availableLevels = AVAILABLE_LEVELS[colorType];
  if (!availableLevels.includes(level)) {
    return false; // 不存在的级别直接返回false
  }
  
  // 检查可见性设置
  const levelKey = `showLevel${level}` as keyof ColorVisibility;
  return visibility[levelKey] ?? true;
}, []);
```

**验收标准**:
- [ ] 不存在的级别永远不显示
- [ ] 存在的级别按可见性设置正确显示
- [ ] 性能不低于原实现

### Debug-3.3: UI控制逻辑优化
**目标**: 确保BasicDataPanel UI与数据状态严格对应  
**时间估计**: 2小时  

**具体任务**:
- [ ] 修改 `BasicDataPanel.getAvailableLevels` 使用正确的数据源
- [ ] 优化级别按钮渲染逻辑，只显示真实存在的级别
- [ ] 改进级别切换的即时反馈
- [ ] 添加级别操作的错误处理

**实现策略**:
```typescript
const renderLevelControls = () => {
  const currentColor = activeColorTab as BasicColorType;
  const availableLevels = AVAILABLE_LEVELS[currentColor]; // 直接使用权威数据源
  const currentVisibility = colorVisibility[currentColor];
  
  return (
    <div className="grid grid-cols-4 gap-1">
      {[1, 2, 3, 4].map((level) => {
        const levelExists = availableLevels.includes(level);
        if (!levelExists) {
          return <div key={level} className="opacity-30">-</div>; // 不存在的级别显示占位符
        }
        
        const levelKey = `showLevel${level}` as keyof ColorVisibility;
        const isActive = currentVisibility[levelKey];
        // ... 渲染存在的级别按钮
      })}
    </div>
  );
};
```

**验收标准**:
- [ ] UI只显示真实存在的级别
- [ ] 级别状态切换立即生效
- [ ] 不存在级别的操作被正确阻止

---

## Phase Debug-4: 系统测试与验证阶段 【P1-高优先级】

### Debug-4.1: 回归测试套件
**目标**: 创建全面的颜色级别显示测试  
**时间估计**: 2小时  

**具体任务**:
- [ ] 创建 `__tests__/colorLevelDisplay.test.ts`
- [ ] 测试所有8种颜色 × 所有定义级别的显示
- [ ] 测试级别切换的状态同步
- [ ] 测试边界情况和错误处理

**测试用例**:
```typescript
describe('颜色级别显示测试', () => {
  test('红色所有4个级别都能正确显示', () => {
    // 测试红色1级、2级、3级、4级的渲染
  });
  
  test('橙色只显示1级、3级、4级，不显示2级', () => {
    // 确保橙色不显示不存在的2级
  });
  
  test('级别切换立即反映到渲染结果', () => {
    // 测试切换响应性
  });
});
```

**验收标准**:
- [ ] 所有测试用例通过
- [ ] 覆盖率达到90%+
- [ ] 性能测试通过

### Debug-4.2: 用户验收测试
**目标**: 解决用户报告的具体问题  
**时间估计**: 1小时  

**具体任务**:
- [ ] 验证红色1级、3级不再透明消失
- [ ] 验证红色4级正确激活
- [ ] 测试所有颜色的级别控制一致性
- [ ] 获得用户最终确认

**验收标准**:
- [ ] 用户报告的所有问题解决
- [ ] 用户界面行为符合预期
- [ ] 无新的回归问题

---

## 风险控制与回滚策略

### 高风险操作标识
🚨 **Store数据结构修改** - 可能影响LocalStorage兼容性  
🚨 **颜色坐标数据调整** - 可能破坏保存的版本  
🚨 **渲染逻辑大幅修改** - 可能影响性能优化  

### 回滚准备
- [ ] 修改前备份所有配置文件
- [ ] 创建数据迁移脚本
- [ ] 准备快速回滚版本

### 验证检查点
- [ ] 每个Debug Phase完成后用户确认
- [ ] 性能指标不低于97%
- [ ] 无新的功能回归

---

## 成功指标

### 技术指标
- [ ] 所有颜色的所有定义级别都能正确显示
- [ ] UI按钮状态与实际渲染状态100%一致  
- [ ] 数据一致性检查100%通过
- [ ] 性能优化效果保持97%+

### 用户体验指标
- [ ] 红色1级、3级透明消失问题彻底解决
- [ ] 红色4级未激活问题彻底解决
- [ ] 所有颜色级别控制行为一致且可预期
- [ ] 用户确认问题完全修复

---

**任务创建时间**: 2025年6月24日 20:50  
**预计完成时间**: 2025年6月25日 18:00  
**责任开发**: Claude Sonnet 4 (AI Assistant)  
**优先级**: P0 (紧急修复) 