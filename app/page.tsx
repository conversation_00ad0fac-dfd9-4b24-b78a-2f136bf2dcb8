'use client';

import { GridContainer } from '../components/Grid';
import { ControlPanelContainer } from '../components/ControlPanel';
import { usePageLogic } from '../hooks/usePageLogic';
import { useDynamicStyleStore } from '../stores/dynamicStyleStore';
// Debug Phase-1: 导入调试工具
import '../utils/debugHelper';

export default function Page() {
    // 🚨 格子变形bug修复：从store获取虚拟滚动状态
    const { enableVirtualization } = useDynamicStyleStore();
    
    // 使用统一的页面逻辑hook，移除所有重复的业务逻辑
    const {
        // 网格数据
        gridData,
        colorIndex,
        
        // 样式配置
        fontSize,
        matrixMargin,
        cellShape,
        displayMode,
        
        // 核心渲染函数（已memoization优化）
        getCellStyle,
        getCellContent,
        getCircleScaleStyle,
        
        // 事件处理函数（已memoization优化）
        onCellClick,
        handleHoverInfo,
        
        // UI状态
        interactionState,
        effectiveActivePanel,
        
        // 组件Props（已memoization优化）
        controlPanelProps,
        
        // Toast状态
        showToast,
        toastMessage,
        toastType,
    } = usePageLogic();

    return (
        <div className="flex h-screen bg-gray-900 text-white">
            <div className="flex-grow flex flex-col relative">
                <GridContainer
                    gridData={gridData}
                    getCellStyle={getCellStyle}
                    getCellContent={getCellContent}
                    getCircleScaleStyle={getCircleScaleStyle}
                    onCellClick={onCellClick}
                    setHoverInfo={handleHoverInfo}
                    colorIndex={colorIndex}
                    fontSize={fontSize}
                    matrixMargin={matrixMargin}
                    specialCoordinates={new Map()}
                    cellShape={cellShape}
                    displayMode={displayMode}
                    hoverInfo={interactionState.hoverInfo?.content || '将鼠标悬停在格子上查看详细信息'}
                    enableVirtualization={enableVirtualization}
                />
                {showToast && (
                    <div className={`fixed top-5 right-5 p-4 rounded-md text-white ${toastType === 'success' ? 'bg-green-600' : toastType === 'error' ? 'bg-red-600' : 'bg-blue-600'}`}>
                        {toastMessage}
                    </div>
                )}
            </div>
            <ControlPanelContainer {...controlPanelProps} />
        </div>
    );
}